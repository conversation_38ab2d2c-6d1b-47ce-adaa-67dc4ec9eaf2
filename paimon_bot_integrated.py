#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Paimon Bot Integrado - Bot de Twitch con Interfaz de Gestión
Incluye gestor de comandos integrado y configuración inicial
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import os
import threading
import asyncio
from datetime import datetime
import uuid
import re

# Importaciones del bot original
import aiohttp
import random
import time
from twitchio.ext import commands

class PaimonBotConfig:
    """Clase para manejar la configuración inicial del bot"""
    
    def __init__(self):
        self.config = {
            "bot_info": {
                "name": "Paimon Bot",
                "nick": "guisodepaimon",
                "token": "oauth:fxyp5yny604oheoipw08d6bvnjw0tz",
                "channels": [],
                "selected_channel": ""
            },
            "ui_settings": {
                "show_on_startup": True,
                "auto_start_bot": False,
                "minimize_to_tray": False
            }
        }
        self.config_file = "paimon_bot_config.paimon"
        self.load_config()
    
    def load_config(self):
        """Carga la configuración del bot"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"✅ Configuración del bot cargada desde {self.config_file}")
            else:
                print(f"📄 Archivo {self.config_file} no existe, usando configuración por defecto")
                self.save_config()
        except Exception as e:
            print(f"❌ Error cargando configuración del bot: {e}")
    
    def save_config(self):
        """Guarda la configuración del bot"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"💾 Configuración del bot guardada en {self.config_file}")
        except Exception as e:
            print(f"❌ Error guardando configuración del bot: {e}")

class PaimonBotSetup:
    """Ventana de configuración inicial del bot"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.result = None
        self.setup_window()
    
    def setup_window(self):
        """Configura la ventana de setup inicial"""
        self.root = tk.Tk()
        self.root.title("🤖 Paimon Bot - Configuración Inicial")
        self.root.geometry("600x500")
        self.root.configure(bg='#0f0f1a')
        self.root.resizable(False, False)
        
        # Centrar ventana
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
        
        # Configurar estilo
        self.setup_style()
        
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Custom.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # Título
        title_label = ttk.Label(main_frame, 
                               text="🤖 Configuración Inicial de Paimon Bot", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # Información del bot
        info_frame = ttk.LabelFrame(main_frame, text="Información del Bot", padding=20)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Variables
        self.bot_name_var = tk.StringVar(value=self.config_manager.config["bot_info"]["name"])
        self.bot_nick_var = tk.StringVar(value=self.config_manager.config["bot_info"]["nick"])
        self.bot_token_var = tk.StringVar(value=self.config_manager.config["bot_info"]["token"])
        
        ttk.Label(info_frame, text="Nombre del Bot:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.bot_name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(info_frame, text="Nick del Bot:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.bot_nick_var, width=40).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(info_frame, text="Token OAuth:").grid(row=2, column=0, sticky=tk.W, pady=5)
        token_entry = ttk.Entry(info_frame, textvariable=self.bot_token_var, width=40, show="*")
        token_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Configuración de canal
        channel_frame = ttk.LabelFrame(main_frame, text="Configuración de Canal", padding=20)
        channel_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(channel_frame, text="Canal de Twitch:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.channel_var = tk.StringVar(value=self.config_manager.config["bot_info"].get("selected_channel", ""))
        channel_entry = ttk.Entry(channel_frame, textvariable=self.channel_var, width=30)
        channel_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(channel_frame, text="(Sin el símbolo @, ej: flozwer)", 
                 style='Help.TLabel').grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Canales guardados
        saved_frame = ttk.LabelFrame(channel_frame, text="Canales Guardados", padding=10)
        saved_frame.grid(row=2, column=0, columnspan=2, sticky=tk.EW, pady=(15, 0))
        
        # Lista de canales guardados
        self.channels_listbox = tk.Listbox(saved_frame, height=4, 
                                          bg='#1a1a2e', fg='#ffffff',
                                          selectbackground='#3a3a4e')
        self.channels_listbox.pack(fill=tk.X, pady=(0, 10))
        
        # Botones para gestionar canales
        channels_buttons = ttk.Frame(saved_frame, style='Custom.TFrame')
        channels_buttons.pack(fill=tk.X)
        
        ttk.Button(channels_buttons, text="➕ Agregar Canal", 
                  command=self.add_channel).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(channels_buttons, text="🗑️ Eliminar Canal", 
                  command=self.remove_channel).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(channels_buttons, text="📋 Seleccionar", 
                  command=self.select_channel).pack(side=tk.LEFT)
        
        # Opciones adicionales
        options_frame = ttk.LabelFrame(main_frame, text="Opciones", padding=15)
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.auto_start_var = tk.BooleanVar(value=self.config_manager.config["ui_settings"]["auto_start_bot"])
        self.show_manager_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="Iniciar bot automáticamente", 
                       variable=self.auto_start_var).pack(anchor=tk.W, pady=2)
        
        ttk.Checkbutton(options_frame, text="Mostrar gestor de comandos", 
                       variable=self.show_manager_var).pack(anchor=tk.W, pady=2)
        
        # Botones principales
        button_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="🚀 Iniciar Bot", 
                  command=self.start_bot).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="⚙️ Solo Gestor", 
                  command=self.open_manager_only).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="❌ Salir", 
                  command=self.exit_app).pack(side=tk.RIGHT)
        
        # Cargar canales guardados
        self.refresh_channels_list()
        
        # Protocolo de cierre
        self.root.protocol("WM_DELETE_WINDOW", self.exit_app)
    
    def setup_style(self):
        """Configura el estilo oscuro para la ventana"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Colores
        bg_dark = '#0f0f1a'
        bg_medium = '#1a1a2e'
        bg_light = '#2a2a3e'
        fg_primary = '#ffffff'
        fg_secondary = '#e8e8e8'
        fg_accent = '#ffd700'
        
        # Configurar estilos
        style.configure('Title.TLabel', 
                       background=bg_dark, foreground=fg_accent, 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Help.TLabel', 
                       background=bg_dark, foreground='#888888', 
                       font=('Arial', 8))
        
        style.configure('Custom.TFrame', background=bg_dark)
        
        style.configure('TLabelframe', 
                       background=bg_dark, foreground=fg_accent,
                       borderwidth=2, relief='solid')
        style.configure('TLabelframe.Label', 
                       background=bg_dark, foreground=fg_accent,
                       font=('Arial', 11, 'bold'))
        
        style.configure('TEntry', 
                       fieldbackground=bg_medium, background=bg_medium,
                       foreground=fg_primary, borderwidth=1,
                       insertcolor=fg_accent)
        
        style.configure('TCheckbutton', 
                       background=bg_dark, foreground=fg_secondary,
                       focuscolor=fg_accent)
        
        style.configure('TButton', 
                       background=bg_light, foreground=fg_primary,
                       borderwidth=1, font=('Arial', 9, 'bold'),
                       relief='flat', padding=(10, 5))
        style.map('TButton',
                 background=[('active', '#3a3a4e'), ('pressed', '#3a3a4e')],
                 foreground=[('active', fg_accent), ('pressed', fg_accent)])
        
        style.configure('TLabel', background=bg_dark, foreground=fg_secondary)
    
    def add_channel(self):
        """Agrega un canal a la lista"""
        channel = self.channel_var.get().strip().lower()
        if not channel:
            messagebox.showwarning("Advertencia", "Ingresa un nombre de canal")
            return
        
        # Remover @ si está presente
        if channel.startswith('@'):
            channel = channel[1:]
        
        if channel not in self.config_manager.config["bot_info"]["channels"]:
            self.config_manager.config["bot_info"]["channels"].append(channel)
            self.refresh_channels_list()
            self.channel_var.set("")
            messagebox.showinfo("Éxito", f"Canal '{channel}' agregado")
        else:
            messagebox.showinfo("Información", f"El canal '{channel}' ya está en la lista")
    
    def remove_channel(self):
        """Elimina el canal seleccionado"""
        selection = self.channels_listbox.curselection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un canal para eliminar")
            return
        
        channel = self.channels_listbox.get(selection[0])
        if messagebox.askyesno("Confirmar", f"¿Eliminar el canal '{channel}'?"):
            self.config_manager.config["bot_info"]["channels"].remove(channel)
            self.refresh_channels_list()
    
    def select_channel(self):
        """Selecciona el canal de la lista"""
        selection = self.channels_listbox.curselection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un canal de la lista")
            return
        
        channel = self.channels_listbox.get(selection[0])
        self.channel_var.set(channel)
    
    def refresh_channels_list(self):
        """Actualiza la lista de canales"""
        self.channels_listbox.delete(0, tk.END)
        for channel in self.config_manager.config["bot_info"]["channels"]:
            self.channels_listbox.insert(tk.END, channel)
    
    def start_bot(self):
        """Inicia el bot con la configuración actual"""
        if not self.channel_var.get().strip():
            messagebox.showerror("Error", "Debes especificar un canal para el bot")
            return
        
        # Guardar configuración
        self.save_current_config()
        
        self.result = {
            "action": "start_bot",
            "show_manager": self.show_manager_var.get()
        }
        self.root.destroy()
    
    def open_manager_only(self):
        """Abre solo el gestor de comandos"""
        self.save_current_config()
        
        self.result = {
            "action": "manager_only",
            "show_manager": True
        }
        self.root.destroy()
    
    def save_current_config(self):
        """Guarda la configuración actual"""
        self.config_manager.config["bot_info"]["name"] = self.bot_name_var.get()
        self.config_manager.config["bot_info"]["nick"] = self.bot_nick_var.get()
        self.config_manager.config["bot_info"]["token"] = self.bot_token_var.get()
        self.config_manager.config["bot_info"]["selected_channel"] = self.channel_var.get().strip().lower()
        self.config_manager.config["ui_settings"]["auto_start_bot"] = self.auto_start_var.get()
        
        self.config_manager.save_config()
    
    def exit_app(self):
        """Sale de la aplicación"""
        self.result = {"action": "exit"}
        self.root.destroy()
    
    def run(self):
        """Ejecuta la ventana de configuración"""
        self.root.mainloop()
        return self.result

class PaimonBot(commands.Bot):
    """Bot principal de Paimon integrado con interfaz"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.bot_config = config_manager.config["bot_info"]

        # Configurar bot
        super().__init__(
            token=self.bot_config["token"],
            prefix='!',
            initial_channels=[self.bot_config["selected_channel"]],
            nick=self.bot_config["nick"]
        )

        # Variables del bot original
        self.lista_ids_per_channel = {}
        self.sorteos_activos = {}
        self.user_data = {}
        self.level_stats = {}
        self.channel_config = {}
        self.banned_words = {}
        self.permissions = set()
        self.last_level_data = None
        self.streamer = self.bot_config["selected_channel"].lower()

        # Cargar datos persistentes
        self.load_persistent_data()

        print(f"🤖 Bot inicializado para el canal: {self.bot_config['selected_channel']}")

    async def event_ready(self):
        """Evento cuando el bot se conecta"""
        print(f'🚀 {self.nick} está conectado y listo!')
        print(f'📺 Canal activo: {self.bot_config["selected_channel"]}')
        print(f'🎮 Bot Paimon v2.0 - ¡Listo para la acción!')

    async def event_message(self, message):
        """Procesa mensajes del chat"""
        if message.echo:
            return

        # Procesar comandos
        await self.handle_commands(message)

        # Aquí puedes agregar lógica adicional para procesar mensajes
        # como detección de idiomas, moderación automática, etc.

    def load_persistent_data(self):
        """Carga datos persistentes del bot"""
        try:
            # Cargar permisos
            if os.path.exists("permissions.js"):
                with open("permissions.js", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.permissions = set(data.get("permissions", []))

            # Cargar datos de usuarios
            if os.path.exists("user_data.json"):
                with open("user_data.json", 'r', encoding='utf-8') as f:
                    self.user_data = json.load(f)

            print("✅ Datos persistentes cargados")
        except Exception as e:
            print(f"⚠️ Error cargando datos persistentes: {e}")

    def save_persistent_data(self):
        """Guarda datos persistentes del bot"""
        try:
            # Guardar permisos
            with open("permissions.js", 'w', encoding='utf-8') as f:
                json.dump({"permissions": list(self.permissions)}, f, ensure_ascii=False, indent=2)

            # Guardar datos de usuarios
            with open("user_data.json", 'w', encoding='utf-8') as f:
                # Convertir datetime a string para JSON
                user_data_serializable = {}
                for channel, users in self.user_data.items():
                    user_data_serializable[channel] = {}
                    for user, data in users.items():
                        user_data_serializable[channel][user] = data.copy()
                        if 'last_daily' in data and data['last_daily']:
                            user_data_serializable[channel][user]['last_daily'] = data['last_daily'].isoformat()

                json.dump(user_data_serializable, f, ensure_ascii=False, indent=2)

            print("💾 Datos persistentes guardados")
        except Exception as e:
            print(f"❌ Error guardando datos persistentes: {e}")

    # Aquí irían todos los comandos del bot original
    # Por ahora agregamos algunos comandos básicos como ejemplo

    @commands.command(name='paimon')
    async def paimon_info(self, ctx):
        """Información sobre Paimon"""
        await ctx.send("🤖 ¡Hola! Soy Paimon, tu bot de Twitch favorito! ✨ Usa !comandos para ver qué puedo hacer 💝")

    @commands.command(name='comandos')
    async def show_commands(self, ctx):
        """Muestra comandos disponibles"""
        commands_text = (
            "📋 COMANDOS PRINCIPALES:\n"
            "!paimon - Info del bot | !comandos - Esta lista\n"
            "!add <id> - Agregar nivel | !queue - Ver cola\n"
            "!points - Ver puntos | !daily - Puntos diarios\n"
            "🔧 Usa el Gestor de Comandos para configurar más!"
        )
        await ctx.send(commands_text)

    def has_permission(self, ctx):
        """Verifica si el usuario tiene permisos"""
        return (ctx.author.is_mod or
                ctx.author.name.lower() == self.streamer or
                ctx.author.name.lower() in self.permissions)

class PaimonCommandManager:
    """Gestor de comandos integrado (versión simplificada)"""

    def __init__(self, bot_instance=None):
        self.bot_instance = bot_instance
        self.config_file = "paimon_commands.paimon"
        self.config = self.load_default_config()
        self.load_config()

    def load_default_config(self):
        """Carga configuración por defecto"""
        return {
            "bot_info": {
                "name": "Paimon Bot",
                "version": "2.0",
                "author": "FlozWer",
                "description": "Bot completo para Twitch con gestión de niveles GD"
            },
            "commands": {},
            "categories": {
                "levels": {"name": "🎮 Niveles GD", "color": "#00ff88", "enabled": True},
                "points": {"name": "💰 Sistema de Puntos", "color": "#ffd700", "enabled": True},
                "fun": {"name": "😄 Diversión", "color": "#ff69b4", "enabled": True},
                "utility": {"name": "🛠️ Utilidades", "color": "#1e90ff", "enabled": True},
                "custom": {"name": "🌟 Personalizados", "color": "#dda0dd", "enabled": True}
            },
            "settings": {
                "prefix": "!",
                "cooldown_global": 3,
                "cooldown_user": 5,
                "max_message_length": 500
            }
        }

    def load_config(self):
        """Carga configuración desde archivo"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"✅ Configuración de comandos cargada desde {self.config_file}")
        except Exception as e:
            print(f"❌ Error cargando configuración de comandos: {e}")

    def save_config(self):
        """Guarda configuración a archivo"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"💾 Configuración de comandos guardada en {self.config_file}")
        except Exception as e:
            print(f"❌ Error guardando configuración de comandos: {e}")

    def show_manager_window(self):
        """Muestra la ventana del gestor de comandos"""
        manager_window = tk.Toplevel()
        manager_window.title("🤖 Paimon Bot - Gestor de Comandos")
        manager_window.geometry("1000x700")
        manager_window.configure(bg='#0f0f1a')

        # Frame principal
        main_frame = ttk.Frame(manager_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Título
        title_label = tk.Label(main_frame,
                              text="🤖 Gestor de Comandos - Paimon Bot",
                              bg='#0f0f1a', fg='#ffd700',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Información del bot conectado
        if self.bot_instance:
            info_text = f"🔗 Bot conectado al canal: {self.bot_instance.bot_config['selected_channel']}"
            status_label = tk.Label(main_frame, text=info_text,
                                   bg='#0f0f1a', fg='#00ff88',
                                   font=('Arial', 12))
            status_label.pack(pady=(0, 15))

        # Notebook para pestañas
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Pestaña de comandos
        commands_frame = ttk.Frame(notebook)
        notebook.add(commands_frame, text="🤖 Comandos")

        # Lista de comandos
        commands_text = tk.Text(commands_frame,
                               bg='#1a1a2e', fg='#ffffff',
                               wrap=tk.WORD, height=20)
        commands_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Mostrar comandos disponibles
        commands_info = """📋 COMANDOS DISPONIBLES:

🤖 BÁSICOS:
• !paimon - Información del bot
• !comandos - Lista de comandos

🎮 NIVELES (Próximamente):
• !add <id> - Agregar nivel a la cola
• !queue - Ver cola de niveles
• !P - Ver tu posición

💰 PUNTOS (Próximamente):
• !points - Ver tus puntos
• !daily - Reclamar puntos diarios
• !shop - Ver tienda

⚙️ CONFIGURACIÓN:
Este gestor te permitirá crear y configurar todos los comandos del bot.
Próximamente se agregará la funcionalidad completa de edición.

🔧 ESTADO ACTUAL:
- Bot conectado y funcionando
- Comandos básicos activos
- Gestor en desarrollo

💡 PRÓXIMAS CARACTERÍSTICAS:
- Editor visual de comandos
- Sistema de permisos
- Categorías personalizables
- Exportación de configuración
"""

        commands_text.insert('1.0', commands_info)
        commands_text.config(state=tk.DISABLED)

        # Pestaña de configuración
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ Configuración")

        config_text = tk.Text(config_frame,
                             bg='#1a1a2e', fg='#ffffff',
                             wrap=tk.WORD, height=20)
        config_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        config_info = f"""⚙️ CONFIGURACIÓN ACTUAL:

🤖 BOT:
• Nombre: {self.config['bot_info']['name']}
• Versión: {self.config['bot_info']['version']}
• Prefijo: {self.config['settings']['prefix']}

📺 CANAL:
• Canal activo: {self.bot_instance.bot_config['selected_channel'] if self.bot_instance else 'No conectado'}
• Nick del bot: {self.bot_instance.bot_config['nick'] if self.bot_instance else 'No configurado'}

🔧 CONFIGURACIÓN:
• Cooldown global: {self.config['settings']['cooldown_global']} segundos
• Cooldown por usuario: {self.config['settings']['cooldown_user']} segundos
• Longitud máxima: {self.config['settings']['max_message_length']} caracteres

📂 CATEGORÍAS:
"""

        for cat_id, cat in self.config['categories'].items():
            status = "✅" if cat['enabled'] else "❌"
            config_info += f"• {status} {cat['name']}\n"

        config_text.insert('1.0', config_info)
        config_text.config(state=tk.DISABLED)

        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Button(button_frame, text="💾 Guardar Configuración",
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="🔄 Recargar",
                  command=lambda: self.refresh_manager_window(manager_window)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="❌ Cerrar",
                  command=manager_window.destroy).pack(side=tk.RIGHT)

    def refresh_manager_window(self, window):
        """Actualiza la ventana del gestor"""
        window.destroy()
        self.show_manager_window()

class PaimonBotApplication:
    """Aplicación principal que integra bot e interfaz"""

    def __init__(self):
        self.config_manager = PaimonBotConfig()
        self.bot_instance = None
        self.command_manager = None
        self.bot_thread = None
        self.bot_loop = None

    def run(self):
        """Ejecuta la aplicación principal"""
        print("🌟 Iniciando Paimon Bot - Aplicación Integrada")
        print("=" * 60)

        # Mostrar ventana de configuración inicial
        setup = PaimonBotSetup(self.config_manager)
        result = setup.run()

        if not result or result["action"] == "exit":
            print("👋 Saliendo de la aplicación")
            return

        # Procesar resultado de la configuración
        if result["action"] == "start_bot":
            self.start_bot_with_manager(result["show_manager"])
        elif result["action"] == "manager_only":
            self.start_manager_only()

    def start_bot_with_manager(self, show_manager=True):
        """Inicia el bot junto con el gestor de comandos"""
        try:
            print(f"🚀 Iniciando bot para el canal: {self.config_manager.config['bot_info']['selected_channel']}")

            # Crear instancia del bot
            self.bot_instance = PaimonBot(self.config_manager)
            self.command_manager = PaimonCommandManager(self.bot_instance)

            # Iniciar bot en hilo separado
            self.start_bot_thread()

            # Mostrar gestor si está habilitado
            if show_manager:
                self.show_integrated_interface()
            else:
                # Solo mostrar ventana de estado
                self.show_status_window()

        except Exception as e:
            messagebox.showerror("Error", f"Error al iniciar el bot: {e}")
            print(f"❌ Error iniciando bot: {e}")

    def start_manager_only(self):
        """Inicia solo el gestor de comandos"""
        print("⚙️ Iniciando solo el gestor de comandos")
        self.command_manager = PaimonCommandManager()
        self.show_integrated_interface()

    def start_bot_thread(self):
        """Inicia el bot en un hilo separado"""
        def run_bot():
            try:
                # Crear nuevo loop para el bot
                self.bot_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.bot_loop)

                # Ejecutar bot
                self.bot_loop.run_until_complete(self.bot_instance.start())
            except Exception as e:
                print(f"❌ Error en el hilo del bot: {e}")

        self.bot_thread = threading.Thread(target=run_bot, daemon=True)
        self.bot_thread.start()
        print("🔄 Bot iniciado en hilo separado")

    def show_integrated_interface(self):
        """Muestra la interfaz integrada principal"""
        # Crear ventana principal
        main_window = tk.Tk()
        main_window.title("🤖 Paimon Bot - Control Central")
        main_window.geometry("800x600")
        main_window.configure(bg='#0f0f1a')

        # Centrar ventana
        main_window.update_idletasks()
        x = (main_window.winfo_screenwidth() // 2) - (main_window.winfo_width() // 2)
        y = (main_window.winfo_screenheight() // 2) - (main_window.winfo_height() // 2)
        main_window.geometry(f"+{x}+{y}")

        # Frame principal
        main_frame = tk.Frame(main_window, bg='#0f0f1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Título
        title_label = tk.Label(main_frame,
                              text="🤖 Paimon Bot - Control Central",
                              bg='#0f0f1a', fg='#ffd700',
                              font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))

        # Estado del bot
        if self.bot_instance:
            status_text = f"🟢 Bot ACTIVO en canal: {self.bot_instance.bot_config['selected_channel']}"
            status_color = '#00ff88'
        else:
            status_text = "🔴 Bot INACTIVO - Solo gestor de comandos"
            status_color = '#ff6b6b'

        status_label = tk.Label(main_frame, text=status_text,
                               bg='#0f0f1a', fg=status_color,
                               font=('Arial', 14, 'bold'))
        status_label.pack(pady=(0, 30))

        # Botones principales
        buttons_frame = tk.Frame(main_frame, bg='#0f0f1a')
        buttons_frame.pack(expand=True)

        # Botón gestor de comandos
        manager_btn = tk.Button(buttons_frame,
                               text="⚙️ Gestor de Comandos",
                               bg='#2a2a3e', fg='#ffffff',
                               font=('Arial', 12, 'bold'),
                               width=20, height=2,
                               relief='flat',
                               command=self.command_manager.show_manager_window)
        manager_btn.pack(pady=10)

        # Botón configuración del bot
        config_btn = tk.Button(buttons_frame,
                              text="🔧 Configurar Bot",
                              bg='#2a2a3e', fg='#ffffff',
                              font=('Arial', 12, 'bold'),
                              width=20, height=2,
                              relief='flat',
                              command=self.show_bot_config)
        config_btn.pack(pady=10)

        # Botón estado del bot
        if self.bot_instance:
            bot_btn_text = "🔴 Detener Bot"
            bot_btn_command = self.stop_bot
        else:
            bot_btn_text = "🚀 Iniciar Bot"
            bot_btn_command = self.restart_bot_setup

        bot_btn = tk.Button(buttons_frame,
                           text=bot_btn_text,
                           bg='#2a2a3e', fg='#ffffff',
                           font=('Arial', 12, 'bold'),
                           width=20, height=2,
                           relief='flat',
                           command=bot_btn_command)
        bot_btn.pack(pady=10)

        # Información adicional
        info_frame = tk.Frame(main_frame, bg='#1a1a2e', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, pady=(30, 0))

        info_text = f"""📊 INFORMACIÓN DEL SISTEMA:

🤖 Bot: {self.config_manager.config['bot_info']['name']}
📺 Canal: {self.config_manager.config['bot_info'].get('selected_channel', 'No configurado')}
⚙️ Gestor: {'Activo' if self.command_manager else 'Inactivo'}
🔧 Configuración: {self.command_manager.config_file if self.command_manager else 'No cargada'}

💡 Usa los botones de arriba para gestionar tu bot Paimon"""

        info_label = tk.Label(info_frame, text=info_text,
                             bg='#1a1a2e', fg='#e8e8e8',
                             font=('Arial', 10),
                             justify=tk.LEFT)
        info_label.pack(padx=15, pady=15)

        # Protocolo de cierre
        def on_closing():
            if self.bot_instance:
                self.stop_bot()
            main_window.destroy()

        main_window.protocol("WM_DELETE_WINDOW", on_closing)

        # Ejecutar interfaz
        main_window.mainloop()

    def show_status_window(self):
        """Muestra ventana de estado simple"""
        status_window = tk.Tk()
        status_window.title("🤖 Paimon Bot - Estado")
        status_window.geometry("400x300")
        status_window.configure(bg='#0f0f1a')

        # Centrar ventana
        status_window.update_idletasks()
        x = (status_window.winfo_screenwidth() // 2) - (status_window.winfo_width() // 2)
        y = (status_window.winfo_screenheight() // 2) - (status_window.winfo_height() // 2)
        status_window.geometry(f"+{x}+{y}")

        # Contenido
        main_frame = tk.Frame(status_window, bg='#0f0f1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        title_label = tk.Label(main_frame,
                              text="🤖 Paimon Bot Activo",
                              bg='#0f0f1a', fg='#ffd700',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        status_text = f"""🟢 Bot conectado y funcionando

📺 Canal: {self.bot_instance.bot_config['selected_channel']}
🤖 Nick: {self.bot_instance.bot_config['nick']}

El bot está activo en el chat.
Puedes cerrar esta ventana."""

        status_label = tk.Label(main_frame, text=status_text,
                               bg='#0f0f1a', fg='#e8e8e8',
                               font=('Arial', 12),
                               justify=tk.CENTER)
        status_label.pack(expand=True)

        # Botón cerrar
        close_btn = tk.Button(main_frame,
                             text="❌ Cerrar",
                             bg='#2a2a3e', fg='#ffffff',
                             font=('Arial', 10, 'bold'),
                             command=status_window.destroy)
        close_btn.pack(pady=(20, 0))

        status_window.mainloop()

    def show_bot_config(self):
        """Muestra configuración del bot"""
        setup = PaimonBotSetup(self.config_manager)
        result = setup.run()

        if result and result["action"] == "start_bot":
            # Reiniciar bot con nueva configuración
            if self.bot_instance:
                self.stop_bot()
            self.start_bot_with_manager(result["show_manager"])

    def stop_bot(self):
        """Detiene el bot"""
        try:
            if self.bot_instance:
                # Guardar datos antes de cerrar
                self.bot_instance.save_persistent_data()

                # Cerrar bot
                if self.bot_loop:
                    self.bot_loop.call_soon_threadsafe(self.bot_loop.stop)

                self.bot_instance = None
                print("🔴 Bot detenido")
                messagebox.showinfo("Bot Detenido", "El bot ha sido detenido correctamente")
        except Exception as e:
            print(f"❌ Error deteniendo bot: {e}")

    def restart_bot_setup(self):
        """Reinicia la configuración del bot"""
        self.show_bot_config()

def main():
    """Función principal"""
    try:
        app = PaimonBotApplication()
        app.run()
    except Exception as e:
        print(f"❌ Error crítico en la aplicación: {e}")
        messagebox.showerror("Error Crítico", f"Error en la aplicación: {e}")

if __name__ == "__main__":
    main()
