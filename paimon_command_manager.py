#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Paimon Bot - Gestor Completo de Comandos
Interfaz gráfica para gestionar todos los comandos del bot
Archivo de configuración: .PaimonCommands
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import os
from datetime import datetime
import uuid
import re

class PaimonCommandManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Paimon Bot - Gestor Completo de Comandos")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0f0f1a')
        
        # Configuración por defecto con comandos existentes del bot
        self.config = {
            "bot_info": {
                "name": "Paimon <PERSON>",
                "version": "2.0",
                "author": "FlozWer",
                "description": "Bot completo para Twitch con gestión de niveles GD"
            },
            "commands": {},
            "categories": {
                "levels": {"name": "🎮 Niveles GD", "color": "#00ff88", "enabled": True},
                "points": {"name": "💰 Sistema de Puntos", "color": "#ffd700", "enabled": True},
                "raffles": {"name": "🎁 Sorteos", "color": "#ff6b6b", "enabled": True},
                "games": {"name": "🎲 Juegos", "color": "#8a2be2", "enabled": True},
                "moderation": {"name": "⚙️ Moderación", "color": "#ff8c00", "enabled": True},
                "utility": {"name": "🛠️ Utilidades", "color": "#1e90ff", "enabled": True},
                "fun": {"name": "😄 Diversión", "color": "#ff69b4", "enabled": True},
                "obs": {"name": "📺 OBS", "color": "#32cd32", "enabled": True},
                "custom": {"name": "🌟 Personalizados", "color": "#dda0dd", "enabled": True}
            },
            "settings": {
                "prefix": "!",
                "cooldown_global": 3,
                "cooldown_user": 5,
                "max_message_length": 500,
                "auto_save": True,
                "backup_enabled": True,
                "case_sensitive": False
            },
            "permissions": {
                "everyone": ["levels", "points", "games", "fun", "utility"],
                "subscribers": ["levels", "points", "games", "fun", "utility", "raffles"],
                "moderators": ["all"],
                "broadcaster": ["all"]
            },
            "created_date": datetime.now().isoformat(),
            "last_modified": datetime.now().isoformat(),
            "version": "2.0"
        }
        
        self.config_file = ".PaimonCommands"
        self.load_existing_commands()
        self.load_config()
        self.setup_ui()
        
    def load_existing_commands(self):
        """Carga los comandos existentes del bot desde el análisis del código"""
        existing_commands = {
            # Comandos de Niveles GD
            "add": {
                "name": "add",
                "category": "levels",
                "description": "Agrega un nivel a la cola por ID",
                "usage": "!add <id>",
                "aliases": [],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "id", "type": "string", "required": True, "description": "ID del nivel de Geometry Dash"}]
            },
            "lvl": {
                "name": "lvl",
                "category": "levels",
                "description": "Muestra el siguiente nivel en la cola",
                "usage": "!lvl",
                "aliases": ["level", "next"],
                "cooldown": 3,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            "queue": {
                "name": "queue",
                "category": "levels",
                "description": "Muestra la cola de niveles",
                "usage": "!queue",
                "aliases": ["cola", "q"],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            "P": {
                "name": "P",
                "category": "levels",
                "description": "Muestra tu posición en la cola",
                "usage": "!P",
                "aliases": ["pos", "position"],
                "cooldown": 3,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            
            # Comandos de Puntos
            "points": {
                "name": "points",
                "category": "points",
                "description": "Muestra tus puntos actuales",
                "usage": "!points",
                "aliases": ["pts", "puntos"],
                "cooldown": 3,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            "daily": {
                "name": "daily",
                "category": "points",
                "description": "Reclama puntos diarios",
                "usage": "!daily",
                "aliases": ["diario"],
                "cooldown": 86400,  # 24 horas
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            "shop": {
                "name": "shop",
                "category": "points",
                "description": "Muestra la tienda de puntos",
                "usage": "!shop",
                "aliases": ["tienda", "store"],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            "buy": {
                "name": "buy",
                "category": "points",
                "description": "Compra un item de la tienda",
                "usage": "!buy <item>",
                "aliases": ["comprar"],
                "cooldown": 10,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "item", "type": "string", "required": True, "description": "Item a comprar"}]
            },
            
            # Comandos de Sorteos
            "sorteo": {
                "name": "sorteo",
                "category": "raffles",
                "description": "Inicia un sorteo",
                "usage": "!sorteo <premio> [duración]",
                "aliases": ["raffle"],
                "cooldown": 30,
                "permission": "moderators",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [
                    {"name": "premio", "type": "string", "required": True, "description": "Premio del sorteo"},
                    {"name": "duración", "type": "integer", "required": False, "description": "Duración en minutos"}
                ]
            },
            "participar": {
                "name": "participar",
                "category": "raffles",
                "description": "Participa en el sorteo activo",
                "usage": "!participar",
                "aliases": ["join", "entrar"],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            
            # Comandos de Juegos
            "8ball": {
                "name": "8ball",
                "category": "games",
                "description": "Pregunta a la bola mágica",
                "usage": "!8ball <pregunta>",
                "aliases": ["bola", "magic"],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "pregunta", "type": "string", "required": True, "description": "Tu pregunta"}]
            },
            "roll": {
                "name": "roll",
                "category": "games",
                "description": "Lanza un dado",
                "usage": "!roll [número]",
                "aliases": ["dado", "dice"],
                "cooldown": 3,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "número", "type": "integer", "required": False, "description": "Número máximo del dado"}]
            },
            
            # Comandos de Diversión
            "hug": {
                "name": "hug",
                "category": "fun",
                "description": "Abraza a alguien",
                "usage": "!hug <usuario>",
                "aliases": ["abrazo"],
                "cooldown": 3,
                "permission": "everyone",
                "enabled": True,
                "response_type": "template",
                "response_text": "🤗 {user} abraza a {target}!",
                "parameters": [{"name": "usuario", "type": "string", "required": True, "description": "Usuario a abrazar"}]
            },
            
            # Comandos de Utilidades
            "time": {
                "name": "time",
                "category": "utility",
                "description": "Muestra la hora actual",
                "usage": "!time [zona]",
                "aliases": ["hora", "clock"],
                "cooldown": 5,
                "permission": "everyone",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "zona", "type": "string", "required": False, "description": "Zona horaria"}]
            },
            
            # Comandos de Moderación
            "remove": {
                "name": "remove",
                "category": "moderation",
                "description": "Remueve un nivel de la cola",
                "usage": "!remove <posición>",
                "aliases": ["rm", "delete"],
                "cooldown": 0,
                "permission": "moderators",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": [{"name": "posición", "type": "integer", "required": True, "description": "Posición en la cola"}]
            },
            "clear": {
                "name": "clear",
                "category": "moderation",
                "description": "Limpia la cola de niveles",
                "usage": "!clear",
                "aliases": ["limpiar"],
                "cooldown": 0,
                "permission": "moderators",
                "enabled": True,
                "response_type": "dynamic",
                "response_text": "",
                "parameters": []
            },
            
            # Comandos Personalizados
            "paimon": {
                "name": "paimon",
                "category": "custom",
                "description": "Información sobre Paimon",
                "usage": "!paimon",
                "aliases": [],
                "cooldown": 10,
                "permission": "everyone",
                "enabled": True,
                "response_type": "static",
                "response_text": "🤖 ¡Hola! Soy Paimon, tu bot de Twitch favorito! ✨ Puedo ayudarte con niveles de Geometry Dash, sorteos, juegos y mucho más! 🎮 Usa !com para ver todos mis comandos 💝 Creado con amor para la comunidad de FlozWer",
                "parameters": []
            }
        }
        
        self.config["commands"] = existing_commands

    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Estilo oscuro
        style = ttk.Style()
        style.theme_use('clam')

        # Configurar tema oscuro completo
        self.configure_dark_theme(style)

        # Frame principal
        main_frame = ttk.Frame(self.root, style='Custom.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Título
        title_label = ttk.Label(main_frame,
                               text="🤖 Paimon Bot - Gestor Completo de Comandos",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # Notebook para pestañas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Pestañas
        self.setup_commands_tab()
        self.setup_categories_tab()
        self.setup_settings_tab()
        self.setup_permissions_tab()
        self.setup_export_tab()

        # Frame de botones
        button_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # Botones principales
        ttk.Button(button_frame, text="💾 Guardar Todo",
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="📁 Cargar Configuración",
                  command=self.load_config_file).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="🔄 Resetear",
                  command=self.reset_config).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="📤 Exportar Bot",
                  command=self.export_bot_code).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="❌ Salir",
                  command=self.root.quit).pack(side=tk.RIGHT)

    def configure_dark_theme(self, style):
        """Configura el tema oscuro completo"""
        # Colores base
        bg_dark = '#0f0f1a'
        bg_medium = '#1a1a2e'
        bg_light = '#2a2a3e'
        fg_primary = '#ffffff'
        fg_secondary = '#e8e8e8'
        fg_accent = '#ffd700'

        # Configurar estilos
        style.configure('Title.TLabel',
                       background=bg_dark, foreground=fg_accent,
                       font=('Arial', 16, 'bold'))

        style.configure('Header.TLabel',
                       background=bg_dark, foreground=fg_secondary,
                       font=('Arial', 12, 'bold'))

        style.configure('Custom.TFrame', background=bg_dark)

        # Notebook
        style.configure('TNotebook', background=bg_dark, borderwidth=0)
        style.configure('TNotebook.Tab',
                       background=bg_medium, foreground=fg_secondary,
                       padding=[20, 8], font=('Arial', 10, 'bold'))
        style.map('TNotebook.Tab',
                 background=[('selected', bg_light), ('active', '#252540')],
                 foreground=[('selected', fg_accent), ('active', fg_primary)])

        # LabelFrame
        style.configure('TLabelframe',
                       background=bg_dark, foreground=fg_accent,
                       borderwidth=2, relief='solid')
        style.configure('TLabelframe.Label',
                       background=bg_dark, foreground=fg_accent,
                       font=('Arial', 11, 'bold'))

        # Entry y otros controles
        style.configure('TEntry',
                       fieldbackground=bg_medium, background=bg_medium,
                       foreground=fg_primary, borderwidth=1,
                       insertcolor=fg_accent)

        style.configure('TCombobox',
                       fieldbackground=bg_medium, background=bg_medium,
                       foreground=fg_primary, borderwidth=1,
                       arrowcolor=fg_accent)

        style.configure('TCheckbutton',
                       background=bg_dark, foreground=fg_secondary,
                       focuscolor=fg_accent)

        style.configure('TButton',
                       background=bg_light, foreground=fg_primary,
                       borderwidth=1, font=('Arial', 9, 'bold'))
        style.map('TButton',
                 background=[('active', '#3a3a4e'), ('pressed', bg_medium)],
                 foreground=[('active', fg_accent)])

        # Treeview
        style.configure('Treeview',
                       background=bg_medium, foreground=fg_primary,
                       fieldbackground=bg_medium, borderwidth=0)
        style.configure('Treeview.Heading',
                       background=bg_light, foreground=fg_accent,
                       font=('Arial', 10, 'bold'))
        style.map('Treeview',
                 background=[('selected', '#3a3a4e')],
                 foreground=[('selected', fg_accent)])

        # Scrollbar
        style.configure('Vertical.TScrollbar',
                       background=bg_light, troughcolor=bg_medium,
                       borderwidth=0, arrowcolor=fg_accent)

        # Labels normales
        style.configure('TLabel', background=bg_dark, foreground=fg_secondary)

    def setup_commands_tab(self):
        """Configura la pestaña de gestión de comandos"""
        commands_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(commands_frame, text="🤖 Comandos")

        # Frame superior para controles
        controls_frame = ttk.Frame(commands_frame, style='Custom.TFrame')
        controls_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(controls_frame, text="➕ Nuevo Comando",
                  command=self.add_command).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="✏️ Editar Comando",
                  command=self.edit_command).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="🗑️ Eliminar Comando",
                  command=self.delete_command).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="📋 Duplicar Comando",
                  command=self.duplicate_command).pack(side=tk.LEFT, padx=(0, 10))

        # Filtros
        filter_frame = ttk.Frame(commands_frame, style='Custom.TFrame')
        filter_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(filter_frame, text="Filtrar por categoría:").pack(side=tk.LEFT, padx=(0, 10))

        self.category_filter = ttk.Combobox(filter_frame, width=20, state="readonly")
        self.category_filter.pack(side=tk.LEFT, padx=(0, 10))
        self.category_filter.bind('<<ComboboxSelected>>', self.filter_commands)

        ttk.Label(filter_frame, text="Buscar:").pack(side=tk.LEFT, padx=(10, 5))

        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_commands)
        search_entry = ttk.Entry(filter_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT)

        # Lista de comandos
        list_frame = ttk.LabelFrame(commands_frame, text="Lista de Comandos", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Treeview para mostrar comandos
        columns = ('Comando', 'Categoría', 'Descripción', 'Uso', 'Permisos', 'Estado')
        self.commands_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)

        # Configurar columnas
        self.commands_tree.heading('Comando', text='Comando')
        self.commands_tree.heading('Categoría', text='Categoría')
        self.commands_tree.heading('Descripción', text='Descripción')
        self.commands_tree.heading('Uso', text='Uso')
        self.commands_tree.heading('Permisos', text='Permisos')
        self.commands_tree.heading('Estado', text='Estado')

        self.commands_tree.column('Comando', width=100)
        self.commands_tree.column('Categoría', width=120)
        self.commands_tree.column('Descripción', width=250)
        self.commands_tree.column('Uso', width=150)
        self.commands_tree.column('Permisos', width=100)
        self.commands_tree.column('Estado', width=80)

        # Scrollbar para la lista
        scrollbar_commands = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.commands_tree.yview)
        self.commands_tree.configure(yscrollcommand=scrollbar_commands.set)

        self.commands_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_commands.pack(side=tk.RIGHT, fill=tk.Y)

        # Doble clic para editar
        self.commands_tree.bind('<Double-1>', lambda e: self.edit_command())

        self.refresh_commands_list()
        self.update_category_filter()

    def setup_categories_tab(self):
        """Configura la pestaña de gestión de categorías"""
        categories_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(categories_frame, text="📂 Categorías")

        # Controles
        controls_frame = ttk.Frame(categories_frame, style='Custom.TFrame')
        controls_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(controls_frame, text="➕ Nueva Categoría",
                  command=self.add_category).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="✏️ Editar Categoría",
                  command=self.edit_category).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="🗑️ Eliminar Categoría",
                  command=self.delete_category).pack(side=tk.LEFT, padx=(0, 10))

        # Lista de categorías
        list_frame = ttk.LabelFrame(categories_frame, text="Categorías de Comandos", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True)

        columns = ('ID', 'Nombre', 'Color', 'Comandos', 'Estado')
        self.categories_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        self.categories_tree.heading('ID', text='ID')
        self.categories_tree.heading('Nombre', text='Nombre')
        self.categories_tree.heading('Color', text='Color')
        self.categories_tree.heading('Comandos', text='# Comandos')
        self.categories_tree.heading('Estado', text='Estado')

        self.categories_tree.column('ID', width=100)
        self.categories_tree.column('Nombre', width=200)
        self.categories_tree.column('Color', width=100)
        self.categories_tree.column('Comandos', width=100)
        self.categories_tree.column('Estado', width=100)

        scrollbar_cat = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=scrollbar_cat.set)

        self.categories_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_cat.pack(side=tk.RIGHT, fill=tk.Y)

        self.categories_tree.bind('<Double-1>', lambda e: self.edit_category())

        self.refresh_categories_list()

    def setup_settings_tab(self):
        """Configura la pestaña de configuración general"""
        settings_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(settings_frame, text="⚙️ Configuración")

        # Información del bot
        bot_frame = ttk.LabelFrame(settings_frame, text="Información del Bot", padding=15)
        bot_frame.pack(fill=tk.X, pady=(0, 15))

        # Variables para la configuración
        self.bot_name_var = tk.StringVar(value=self.config["bot_info"]["name"])
        self.bot_version_var = tk.StringVar(value=self.config["bot_info"]["version"])
        self.bot_author_var = tk.StringVar(value=self.config["bot_info"]["author"])
        self.bot_desc_var = tk.StringVar(value=self.config["bot_info"]["description"])

        ttk.Label(bot_frame, text="Nombre del Bot:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(bot_frame, textvariable=self.bot_name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(bot_frame, text="Versión:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(bot_frame, textvariable=self.bot_version_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(bot_frame, text="Autor:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(bot_frame, textvariable=self.bot_author_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(bot_frame, text="Descripción:").grid(row=3, column=0, sticky=tk.NW, pady=5)
        desc_entry = ttk.Entry(bot_frame, textvariable=self.bot_desc_var, width=50)
        desc_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Configuración de comandos
        cmd_frame = ttk.LabelFrame(settings_frame, text="Configuración de Comandos", padding=15)
        cmd_frame.pack(fill=tk.X, pady=(0, 15))

        self.prefix_var = tk.StringVar(value=self.config["settings"]["prefix"])
        self.cooldown_global_var = tk.IntVar(value=self.config["settings"]["cooldown_global"])
        self.cooldown_user_var = tk.IntVar(value=self.config["settings"]["cooldown_user"])
        self.max_length_var = tk.IntVar(value=self.config["settings"]["max_message_length"])
        self.case_sensitive_var = tk.BooleanVar(value=self.config["settings"]["case_sensitive"])

        ttk.Label(cmd_frame, text="Prefijo de Comandos:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(cmd_frame, textvariable=self.prefix_var, width=5).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(cmd_frame, text="Cooldown Global (seg):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(cmd_frame, from_=0, to=60, textvariable=self.cooldown_global_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(cmd_frame, text="Cooldown por Usuario (seg):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(cmd_frame, from_=0, to=300, textvariable=self.cooldown_user_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(cmd_frame, text="Longitud Máxima Mensaje:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(cmd_frame, from_=100, to=2000, textvariable=self.max_length_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Checkbutton(cmd_frame, text="Comandos sensibles a mayúsculas",
                       variable=self.case_sensitive_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=10)

        # Opciones adicionales
        options_frame = ttk.LabelFrame(settings_frame, text="Opciones Adicionales", padding=15)
        options_frame.pack(fill=tk.X)

        self.auto_save_var = tk.BooleanVar(value=self.config["settings"]["auto_save"])
        self.backup_var = tk.BooleanVar(value=self.config["settings"]["backup_enabled"])

        ttk.Checkbutton(options_frame, text="Guardado automático",
                       variable=self.auto_save_var).pack(anchor=tk.W, pady=5)

        ttk.Checkbutton(options_frame, text="Crear respaldos automáticos",
                       variable=self.backup_var).pack(anchor=tk.W, pady=5)

    def setup_permissions_tab(self):
        """Configura la pestaña de permisos"""
        permissions_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(permissions_frame, text="🔐 Permisos")

        # Información
        info_frame = ttk.LabelFrame(permissions_frame, text="Configuración de Permisos", padding=15)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        info_text = tk.Text(info_frame, height=4, width=80,
                           bg='#1a1a2e', fg='#ffffff',
                           insertbackground='#ffd700',
                           wrap=tk.WORD, state=tk.DISABLED)
        info_text.pack()

        info_content = """Los permisos determinan qué usuarios pueden usar cada categoría de comandos:
• everyone: Todos los usuarios del chat
• subscribers: Suscriptores del canal
• moderators: Moderadores del canal
• broadcaster: Solo el streamer"""

        info_text.config(state=tk.NORMAL)
        info_text.insert('1.0', info_content)
        info_text.config(state=tk.DISABLED)

        # Lista de permisos
        perms_frame = ttk.LabelFrame(permissions_frame, text="Permisos por Rol", padding=10)
        perms_frame.pack(fill=tk.BOTH, expand=True)

        # Crear checkboxes para cada rol y categoría
        self.permission_vars = {}

        roles = ["everyone", "subscribers", "moderators", "broadcaster"]
        categories = list(self.config["categories"].keys())

        # Headers
        ttk.Label(perms_frame, text="Categoría", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=10, pady=5)
        for i, role in enumerate(roles):
            ttk.Label(perms_frame, text=role.title(), font=('Arial', 10, 'bold')).grid(row=0, column=i+1, padx=10, pady=5)

        # Checkboxes
        for i, category in enumerate(categories):
            cat_name = self.config["categories"][category]["name"]
            ttk.Label(perms_frame, text=cat_name).grid(row=i+1, column=0, sticky=tk.W, padx=10, pady=2)

            self.permission_vars[category] = {}
            for j, role in enumerate(roles):
                var = tk.BooleanVar()
                # Verificar si el rol tiene acceso a esta categoría
                if role in self.config["permissions"]:
                    has_access = (category in self.config["permissions"][role] or
                                "all" in self.config["permissions"][role])
                    var.set(has_access)

                self.permission_vars[category][role] = var
                ttk.Checkbutton(perms_frame, variable=var).grid(row=i+1, column=j+1, padx=20, pady=2)

    def setup_export_tab(self):
        """Configura la pestaña de exportación"""
        export_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(export_frame, text="📤 Exportar")

        # Información
        info_frame = ttk.LabelFrame(export_frame, text="Exportar Configuración", padding=15)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text="Exporta la configuración de comandos para integrar con el bot:").pack(anchor=tk.W, pady=5)

        # Botones de exportación
        buttons_frame = ttk.Frame(info_frame, style='Custom.TFrame')
        buttons_frame.pack(fill=tk.X, pady=10)

        ttk.Button(buttons_frame, text="📄 Exportar JSON",
                  command=self.export_json).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🐍 Exportar Python",
                  command=self.export_python).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="📋 Exportar Documentación",
                  command=self.export_docs).pack(side=tk.LEFT, padx=(0, 10))

        # Vista previa
        preview_frame = ttk.LabelFrame(export_frame, text="Vista Previa", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True)

        self.preview_text = scrolledtext.ScrolledText(preview_frame,
                                                     bg='#1a1a2e', fg='#ffffff',
                                                     insertbackground='#ffd700',
                                                     wrap=tk.WORD, height=20)
        self.preview_text.pack(fill=tk.BOTH, expand=True)

        # Generar vista previa inicial
        self.update_preview()

    # ==================== FUNCIONES DE COMANDOS ====================

    def add_command(self):
        """Abre ventana para agregar nuevo comando"""
        self.command_dialog()

    def edit_command(self):
        """Abre ventana para editar comando seleccionado"""
        selection = self.commands_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un comando para editar")
            return

        item = self.commands_tree.item(selection[0])
        command_name = item['values'][0]

        if command_name in self.config["commands"]:
            self.command_dialog(command_name)

    def delete_command(self):
        """Elimina el comando seleccionado"""
        selection = self.commands_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un comando para eliminar")
            return

        item = self.commands_tree.item(selection[0])
        command_name = item['values'][0]

        if messagebox.askyesno("Confirmar", f"¿Eliminar el comando '{command_name}'?"):
            if command_name in self.config["commands"]:
                del self.config["commands"][command_name]
                self.refresh_commands_list()
                messagebox.showinfo("Éxito", "Comando eliminado correctamente")

    def duplicate_command(self):
        """Duplica el comando seleccionado"""
        selection = self.commands_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un comando para duplicar")
            return

        item = self.commands_tree.item(selection[0])
        command_name = item['values'][0]

        if command_name in self.config["commands"]:
            # Crear copia del comando
            original = self.config["commands"][command_name].copy()
            new_name = f"{command_name}_copy"

            # Asegurar nombre único
            counter = 1
            while new_name in self.config["commands"]:
                new_name = f"{command_name}_copy{counter}"
                counter += 1

            original["name"] = new_name
            self.config["commands"][new_name] = original
            self.refresh_commands_list()
            messagebox.showinfo("Éxito", f"Comando duplicado como '{new_name}'")

    def command_dialog(self, command_name=None):
        """Ventana de diálogo para crear/editar comandos"""
        dialog = tk.Toplevel(self.root)
        dialog.title("➕ Nuevo Comando" if command_name is None else f"✏️ Editar Comando: {command_name}")
        dialog.geometry("700x600")
        dialog.configure(bg='#0f0f1a')
        dialog.transient(self.root)
        dialog.grab_set()

        # Centrar ventana
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Variables
        name_var = tk.StringVar()
        category_var = tk.StringVar(value="custom")
        description_var = tk.StringVar()
        usage_var = tk.StringVar()
        aliases_var = tk.StringVar()
        cooldown_var = tk.IntVar(value=5)
        permission_var = tk.StringVar(value="everyone")
        enabled_var = tk.BooleanVar(value=True)
        response_type_var = tk.StringVar(value="static")
        response_text_var = tk.StringVar()

        # Si estamos editando, cargar datos existentes
        if command_name and command_name in self.config["commands"]:
            cmd = self.config["commands"][command_name]
            name_var.set(cmd["name"])
            category_var.set(cmd["category"])
            description_var.set(cmd["description"])
            usage_var.set(cmd["usage"])
            aliases_var.set(", ".join(cmd["aliases"]))
            cooldown_var.set(cmd["cooldown"])
            permission_var.set(cmd["permission"])
            enabled_var.set(cmd["enabled"])
            response_type_var.set(cmd["response_type"])
            response_text_var.set(cmd["response_text"])

        # Frame principal con scroll
        canvas = tk.Canvas(dialog, bg='#0f0f1a')
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Custom.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Información básica
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Información Básica", padding=15)
        basic_frame.pack(fill=tk.X, pady=(10, 15), padx=20)

        ttk.Label(basic_frame, text="Nombre del Comando:").grid(row=0, column=0, sticky=tk.W, pady=5)
        name_entry = ttk.Entry(basic_frame, textvariable=name_var, width=30)
        name_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Categoría:").grid(row=1, column=0, sticky=tk.W, pady=5)
        category_combo = ttk.Combobox(basic_frame, textvariable=category_var, width=25, state="readonly")
        category_combo['values'] = list(self.config["categories"].keys())
        category_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Descripción:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(basic_frame, textvariable=description_var, width=50).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Uso:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(basic_frame, textvariable=usage_var, width=40).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Aliases (separados por coma):").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(basic_frame, textvariable=aliases_var, width=40).grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Configuración avanzada
        advanced_frame = ttk.LabelFrame(scrollable_frame, text="Configuración Avanzada", padding=15)
        advanced_frame.pack(fill=tk.X, pady=(0, 15), padx=20)

        ttk.Label(advanced_frame, text="Cooldown (segundos):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(advanced_frame, from_=0, to=3600, textvariable=cooldown_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(advanced_frame, text="Permisos:").grid(row=1, column=0, sticky=tk.W, pady=5)
        perm_combo = ttk.Combobox(advanced_frame, textvariable=permission_var, width=20, state="readonly")
        perm_combo['values'] = ['everyone', 'subscribers', 'moderators', 'broadcaster']
        perm_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Checkbutton(advanced_frame, text="Comando activo", variable=enabled_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=10)

        # Respuesta del comando
        response_frame = ttk.LabelFrame(scrollable_frame, text="Respuesta del Comando", padding=15)
        response_frame.pack(fill=tk.X, pady=(0, 15), padx=20)

        ttk.Label(response_frame, text="Tipo de Respuesta:").grid(row=0, column=0, sticky=tk.W, pady=5)
        response_combo = ttk.Combobox(response_frame, textvariable=response_type_var, width=20, state="readonly")
        response_combo['values'] = ['static', 'template', 'dynamic']
        response_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(response_frame, text="Texto de Respuesta:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        response_text = tk.Text(response_frame, width=60, height=4,
                               bg='#1a1a2e', fg='#ffffff',
                               insertbackground='#ffd700',
                               wrap=tk.WORD)
        response_text.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        response_text.insert('1.0', response_text_var.get())

        # Información de ayuda
        help_text = tk.Text(response_frame, width=60, height=3,
                           bg='#1a1a2e', fg='#b8b8b8',
                           wrap=tk.WORD, state=tk.DISABLED)
        help_text.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        help_content = """Tipos de respuesta:
• static: Texto fijo que no cambia
• template: Texto con variables como {user}, {target}, etc.
• dynamic: Respuesta generada por código Python"""

        help_text.config(state=tk.NORMAL)
        help_text.insert('1.0', help_content)
        help_text.config(state=tk.DISABLED)

        # Botones
        button_frame = ttk.Frame(scrollable_frame, style='Custom.TFrame')
        button_frame.pack(fill=tk.X, pady=20, padx=20)

        def save_command():
            name = name_var.get().strip()
            if not name:
                messagebox.showerror("Error", "El nombre del comando es obligatorio")
                return

            # Validar que el nombre no exista (excepto si estamos editando el mismo)
            if name != command_name and name in self.config["commands"]:
                messagebox.showerror("Error", f"Ya existe un comando llamado '{name}'")
                return

            # Crear o actualizar comando
            new_command = {
                "name": name,
                "category": category_var.get(),
                "description": description_var.get(),
                "usage": usage_var.get(),
                "aliases": [alias.strip() for alias in aliases_var.get().split(',') if alias.strip()],
                "cooldown": cooldown_var.get(),
                "permission": permission_var.get(),
                "enabled": enabled_var.get(),
                "response_type": response_type_var.get(),
                "response_text": response_text.get('1.0', tk.END).strip(),
                "parameters": []  # Se puede expandir en el futuro
            }

            # Si estamos editando y el nombre cambió, eliminar el anterior
            if command_name and command_name != name and command_name in self.config["commands"]:
                del self.config["commands"][command_name]

            self.config["commands"][name] = new_command
            self.refresh_commands_list()
            dialog.destroy()
            messagebox.showinfo("Éxito", "Comando guardado correctamente")

        ttk.Button(button_frame, text="💾 Guardar", command=save_command).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancelar", command=dialog.destroy).pack(side=tk.LEFT)

        # Configurar scroll
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Focus en el nombre si es nuevo comando
        if command_name is None:
            name_entry.focus()

    # ==================== FUNCIONES DE CATEGORÍAS ====================

    def add_category(self):
        """Agrega nueva categoría"""
        self.category_dialog()

    def edit_category(self):
        """Edita categoría seleccionada"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona una categoría para editar")
            return

        item = self.categories_tree.item(selection[0])
        category_id = item['values'][0]

        if category_id in self.config["categories"]:
            self.category_dialog(category_id)

    def delete_category(self):
        """Elimina categoría seleccionada"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona una categoría para eliminar")
            return

        item = self.categories_tree.item(selection[0])
        category_id = item['values'][0]

        # Verificar si hay comandos usando esta categoría
        commands_using = [cmd for cmd in self.config["commands"].values() if cmd["category"] == category_id]
        if commands_using:
            messagebox.showerror("Error", f"No se puede eliminar la categoría '{category_id}' porque tiene {len(commands_using)} comandos asignados.")
            return

        if messagebox.askyesno("Confirmar", f"¿Eliminar la categoría '{category_id}'?"):
            if category_id in self.config["categories"]:
                del self.config["categories"][category_id]
                self.refresh_categories_list()
                self.update_category_filter()
                messagebox.showinfo("Éxito", "Categoría eliminada correctamente")

    def category_dialog(self, category_id=None):
        """Ventana de diálogo para crear/editar categorías"""
        dialog = tk.Toplevel(self.root)
        dialog.title("➕ Nueva Categoría" if category_id is None else f"✏️ Editar Categoría: {category_id}")
        dialog.geometry("400x300")
        dialog.configure(bg='#0f0f1a')
        dialog.transient(self.root)
        dialog.grab_set()

        # Variables
        id_var = tk.StringVar()
        name_var = tk.StringVar()
        color_var = tk.StringVar(value="#ffd700")
        enabled_var = tk.BooleanVar(value=True)

        # Si estamos editando, cargar datos existentes
        if category_id and category_id in self.config["categories"]:
            cat = self.config["categories"][category_id]
            id_var.set(category_id)
            name_var.set(cat["name"])
            color_var.set(cat["color"])
            enabled_var.set(cat["enabled"])

        # Frame principal
        main_frame = ttk.Frame(dialog, style='Custom.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Campos
        ttk.Label(main_frame, text="ID de la Categoría:").grid(row=0, column=0, sticky=tk.W, pady=5)
        id_entry = ttk.Entry(main_frame, textvariable=id_var, width=30)
        id_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        if category_id:  # Deshabilitar edición del ID si estamos editando
            id_entry.config(state='readonly')

        ttk.Label(main_frame, text="Nombre de la Categoría:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=name_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(main_frame, text="Color (hex):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=color_var, width=15).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Checkbutton(main_frame, text="Categoría activa", variable=enabled_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=10)

        # Botones
        button_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        def save_category():
            cat_id = id_var.get().strip()
            name = name_var.get().strip()
            color = color_var.get().strip()

            if not cat_id or not name:
                messagebox.showerror("Error", "ID y nombre son obligatorios")
                return

            # Validar color hex
            if not re.match(r'^#[0-9A-Fa-f]{6}$', color):
                messagebox.showerror("Error", "Color debe ser formato hex válido (#RRGGBB)")
                return

            # Validar que el ID no exista (excepto si estamos editando el mismo)
            if cat_id != category_id and cat_id in self.config["categories"]:
                messagebox.showerror("Error", f"Ya existe una categoría con ID '{cat_id}'")
                return

            # Crear o actualizar categoría
            new_category = {
                "name": name,
                "color": color,
                "enabled": enabled_var.get()
            }

            # Si estamos editando y el ID cambió, eliminar el anterior
            if category_id and category_id != cat_id and category_id in self.config["categories"]:
                del self.config["categories"][category_id]
                # Actualizar comandos que usen la categoría anterior
                for cmd in self.config["commands"].values():
                    if cmd["category"] == category_id:
                        cmd["category"] = cat_id

            self.config["categories"][cat_id] = new_category
            self.refresh_categories_list()
            self.update_category_filter()
            dialog.destroy()
            messagebox.showinfo("Éxito", "Categoría guardada correctamente")

        ttk.Button(button_frame, text="💾 Guardar", command=save_category).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancelar", command=dialog.destroy).pack(side=tk.LEFT)

    # ==================== FUNCIONES DE ACTUALIZACIÓN ====================

    def refresh_commands_list(self):
        """Actualiza la lista de comandos en el TreeView"""
        # Limpiar lista actual
        for item in self.commands_tree.get_children():
            self.commands_tree.delete(item)

        # Agregar comandos
        for cmd_name, cmd in self.config["commands"].items():
            category_name = self.config["categories"].get(cmd["category"], {}).get("name", cmd["category"])
            status = "✅ Activo" if cmd["enabled"] else "❌ Inactivo"

            self.commands_tree.insert('', tk.END, values=(
                cmd_name,
                category_name,
                cmd["description"][:40] + "..." if len(cmd["description"]) > 40 else cmd["description"],
                cmd["usage"],
                cmd["permission"],
                status
            ))

    def refresh_categories_list(self):
        """Actualiza la lista de categorías en el TreeView"""
        # Limpiar lista actual
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)

        # Agregar categorías
        for cat_id, cat in self.config["categories"].items():
            # Contar comandos en esta categoría
            cmd_count = len([cmd for cmd in self.config["commands"].values() if cmd["category"] == cat_id])
            status = "✅ Activa" if cat["enabled"] else "❌ Inactiva"

            self.categories_tree.insert('', tk.END, values=(
                cat_id,
                cat["name"],
                cat["color"],
                cmd_count,
                status
            ))

    def update_category_filter(self):
        """Actualiza el combobox de filtro de categorías"""
        categories = ["Todas"] + [cat["name"] for cat in self.config["categories"].values()]
        self.category_filter['values'] = categories
        if not self.category_filter.get():
            self.category_filter.set("Todas")

    def filter_commands(self, *args):
        """Filtra los comandos según categoría y búsqueda"""
        # Obtener filtros
        category_filter = self.category_filter.get()
        search_term = self.search_var.get().lower()

        # Limpiar lista
        for item in self.commands_tree.get_children():
            self.commands_tree.delete(item)

        # Filtrar y mostrar comandos
        for cmd_name, cmd in self.config["commands"].items():
            category_name = self.config["categories"].get(cmd["category"], {}).get("name", cmd["category"])

            # Filtro por categoría
            if category_filter != "Todas" and category_name != category_filter:
                continue

            # Filtro por búsqueda
            if search_term and search_term not in cmd_name.lower() and search_term not in cmd["description"].lower():
                continue

            status = "✅ Activo" if cmd["enabled"] else "❌ Inactivo"

            self.commands_tree.insert('', tk.END, values=(
                cmd_name,
                category_name,
                cmd["description"][:40] + "..." if len(cmd["description"]) > 40 else cmd["description"],
                cmd["usage"],
                cmd["permission"],
                status
            ))

    # ==================== FUNCIONES DE CONFIGURACIÓN ====================

    def load_config(self):
        """Carga la configuración desde el archivo"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Fusionar con configuración por defecto
                    self.config.update(loaded_config)
                print(f"✅ Configuración cargada desde {self.config_file}")
            else:
                print(f"📄 Archivo {self.config_file} no existe, usando configuración por defecto")
                self.save_config()
        except Exception as e:
            messagebox.showerror("Error", f"Error al cargar configuración: {e}")
            print(f"❌ Error cargando configuración: {e}")

    def save_config(self):
        """Guarda la configuración actual al archivo"""
        try:
            # Actualizar configuración con valores actuales de la UI
            self.update_config_from_ui()

            # Crear respaldo si está habilitado
            if self.config["settings"]["backup_enabled"] and os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                import shutil
                shutil.copy2(self.config_file, backup_file)
                print(f"🔄 Respaldo creado: {backup_file}")

            # Guardar configuración
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("Éxito", f"Configuración guardada en {self.config_file}")
            print(f"💾 Configuración guardada en {self.config_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Error al guardar configuración: {e}")
            print(f"❌ Error guardando configuración: {e}")

    def update_config_from_ui(self):
        """Actualiza la configuración con los valores actuales de la interfaz"""
        try:
            # Información del bot
            self.config["bot_info"]["name"] = self.bot_name_var.get()
            self.config["bot_info"]["version"] = self.bot_version_var.get()
            self.config["bot_info"]["author"] = self.bot_author_var.get()
            self.config["bot_info"]["description"] = self.bot_desc_var.get()

            # Configuración de comandos
            self.config["settings"]["prefix"] = self.prefix_var.get()
            self.config["settings"]["cooldown_global"] = self.cooldown_global_var.get()
            self.config["settings"]["cooldown_user"] = self.cooldown_user_var.get()
            self.config["settings"]["max_message_length"] = self.max_length_var.get()
            self.config["settings"]["case_sensitive"] = self.case_sensitive_var.get()
            self.config["settings"]["auto_save"] = self.auto_save_var.get()
            self.config["settings"]["backup_enabled"] = self.backup_var.get()

            # Permisos
            for category, roles in self.permission_vars.items():
                for role, var in roles.items():
                    if role not in self.config["permissions"]:
                        self.config["permissions"][role] = []

                    if var.get():
                        if category not in self.config["permissions"][role] and "all" not in self.config["permissions"][role]:
                            self.config["permissions"][role].append(category)
                    else:
                        if category in self.config["permissions"][role]:
                            self.config["permissions"][role].remove(category)

            self.config["last_modified"] = datetime.now().isoformat()
        except Exception as e:
            print(f"⚠️ Error actualizando configuración desde UI: {e}")

    def load_config_file(self):
        """Carga configuración desde un archivo seleccionado"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo de configuración",
            filetypes=[("Archivos PaimonCommands", "*.PaimonCommands"), ("Archivos JSON", "*.json"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)

                self.config.update(loaded_config)
                self.refresh_ui()
                messagebox.showinfo("Éxito", f"Configuración cargada desde {file_path}")

            except Exception as e:
                messagebox.showerror("Error", f"Error al cargar archivo: {e}")

    def refresh_ui(self):
        """Actualiza la interfaz con los valores de configuración actuales"""
        try:
            # Actualizar variables de la UI
            self.bot_name_var.set(self.config["bot_info"]["name"])
            self.bot_version_var.set(self.config["bot_info"]["version"])
            self.bot_author_var.set(self.config["bot_info"]["author"])
            self.bot_desc_var.set(self.config["bot_info"]["description"])

            self.prefix_var.set(self.config["settings"]["prefix"])
            self.cooldown_global_var.set(self.config["settings"]["cooldown_global"])
            self.cooldown_user_var.set(self.config["settings"]["cooldown_user"])
            self.max_length_var.set(self.config["settings"]["max_message_length"])
            self.case_sensitive_var.set(self.config["settings"]["case_sensitive"])
            self.auto_save_var.set(self.config["settings"]["auto_save"])
            self.backup_var.set(self.config["settings"]["backup_enabled"])

            # Actualizar listas
            self.refresh_commands_list()
            self.refresh_categories_list()
            self.update_category_filter()
            self.update_preview()

        except Exception as e:
            print(f"⚠️ Error actualizando interfaz: {e}")

    def reset_config(self):
        """Resetea la configuración a valores por defecto"""
        if messagebox.askyesno("Confirmar Reset", "¿Estás seguro de que quieres resetear toda la configuración?"):
            self.__init__()  # Reinicializar con valores por defecto
            messagebox.showinfo("Reset Completo", "Configuración reseteada a valores por defecto")

    # ==================== FUNCIONES DE EXPORTACIÓN ====================

    def export_json(self):
        """Exporta la configuración a JSON"""
        file_path = filedialog.asksaveasfilename(
            title="Exportar configuración JSON",
            defaultextension=".json",
            filetypes=[("Archivos JSON", "*.json"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("Éxito", f"Configuración exportada a {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Error al exportar: {e}")

    def export_python(self):
        """Exporta código Python para integrar con el bot"""
        file_path = filedialog.asksaveasfilename(
            title="Exportar código Python",
            defaultextension=".py",
            filetypes=[("Archivos Python", "*.py"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                python_code = self.generate_python_code()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(python_code)
                messagebox.showinfo("Éxito", f"Código Python exportado a {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Error al exportar: {e}")

    def export_docs(self):
        """Exporta documentación de comandos"""
        file_path = filedialog.asksaveasfilename(
            title="Exportar documentación",
            defaultextension=".md",
            filetypes=[("Archivos Markdown", "*.md"), ("Archivos HTML", "*.html"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                if file_path.endswith('.html'):
                    docs = self.generate_html_docs()
                else:
                    docs = self.generate_markdown_docs()

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(docs)
                messagebox.showinfo("Éxito", f"Documentación exportada a {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Error al exportar: {e}")

    def export_bot_code(self):
        """Exporta código completo del bot con todos los comandos"""
        file_path = filedialog.asksaveasfilename(
            title="Exportar bot completo",
            defaultextension=".py",
            filetypes=[("Archivos Python", "*.py"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                bot_code = self.generate_complete_bot_code()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(bot_code)
                messagebox.showinfo("Éxito", f"Bot completo exportado a {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Error al exportar: {e}")

    def update_preview(self):
        """Actualiza la vista previa de exportación"""
        try:
            preview_content = f"""# Resumen de Configuración - {self.config['bot_info']['name']}

## Información General
- **Nombre**: {self.config['bot_info']['name']}
- **Versión**: {self.config['bot_info']['version']}
- **Autor**: {self.config['bot_info']['author']}
- **Prefijo**: {self.config['settings']['prefix']}

## Estadísticas
- **Total de Comandos**: {len(self.config['commands'])}
- **Comandos Activos**: {len([cmd for cmd in self.config['commands'].values() if cmd['enabled']])}
- **Categorías**: {len(self.config['categories'])}

## Comandos por Categoría
"""

            for cat_id, cat in self.config["categories"].items():
                cmd_count = len([cmd for cmd in self.config["commands"].values() if cmd["category"] == cat_id])
                preview_content += f"- **{cat['name']}**: {cmd_count} comandos\n"

            self.preview_text.delete('1.0', tk.END)
            self.preview_text.insert('1.0', preview_content)

        except Exception as e:
            print(f"⚠️ Error actualizando vista previa: {e}")

    def generate_python_code(self):
        """Genera código Python para integrar comandos"""
        code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{self.config["bot_info"]["name"]} - Comandos Generados
Generado automáticamente por Paimon Command Manager
Versión: {self.config["bot_info"]["version"]}
Autor: {self.config["bot_info"]["author"]}
"""

from twitchio.ext import commands
import json
import os

class GeneratedCommands:
    def __init__(self, bot):
        self.bot = bot
        self.config_file = ".PaimonCommands"
        self.load_config()

    def load_config(self):
        """Carga la configuración de comandos"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {{"commands": {{}}, "settings": {{"prefix": "!"}}}}
        except Exception as e:
            print(f"Error cargando configuración: {{e}}")
            self.config = {{"commands": {{}}, "settings": {{"prefix": "!"}}}}

'''

        # Generar comandos
        for cmd_name, cmd in self.config["commands"].items():
            if not cmd["enabled"]:
                continue

            code += f'''
    @commands.command(name='{cmd_name}')
    async def {cmd_name}_command(self, ctx):
        """{cmd["description"]}"""
        # Verificar permisos
        if not self.check_permission(ctx, '{cmd["permission"]}'):
            await ctx.send(f"@{{ctx.author.name}} no tienes permiso para usar este comando.")
            return

        # Verificar cooldown
        if not self.check_cooldown(ctx, '{cmd_name}', {cmd["cooldown"]}):
            return

        # Respuesta del comando
'''

            if cmd["response_type"] == "static":
                code += f'''        await ctx.send("{cmd["response_text"]}")
'''
            elif cmd["response_type"] == "template":
                code += f'''        response = "{cmd["response_text"]}"
        response = response.replace("{{user}}", ctx.author.name)
        # Agregar más variables según necesites
        await ctx.send(response)
'''
            else:  # dynamic
                code += f'''        # Implementar lógica dinámica para {cmd_name}
        await ctx.send(f"@{{ctx.author.name}} comando {{'{cmd_name}'}} ejecutado!")
'''

        # Agregar funciones auxiliares
        code += '''
    def check_permission(self, ctx, required_permission):
        """Verifica si el usuario tiene los permisos necesarios"""
        if required_permission == "everyone":
            return True
        elif required_permission == "subscribers":
            return ctx.author.is_subscriber or ctx.author.is_mod or ctx.author.name.lower() == ctx.channel.name.lower()
        elif required_permission == "moderators":
            return ctx.author.is_mod or ctx.author.name.lower() == ctx.channel.name.lower()
        elif required_permission == "broadcaster":
            return ctx.author.name.lower() == ctx.channel.name.lower()
        return False

    def check_cooldown(self, ctx, command_name, cooldown_seconds):
        """Verifica el cooldown del comando"""
        # Implementar sistema de cooldown
        # Por ahora retorna True
        return True
'''

        return code

    def generate_markdown_docs(self):
        """Genera documentación en Markdown"""
        docs = f"""# {self.config["bot_info"]["name"]} - Documentación de Comandos

**Versión**: {self.config["bot_info"]["version"]}
**Autor**: {self.config["bot_info"]["author"]}
**Descripción**: {self.config["bot_info"]["description"]}

## Configuración

- **Prefijo**: `{self.config["settings"]["prefix"]}`
- **Total de Comandos**: {len(self.config["commands"])}
- **Comandos Activos**: {len([cmd for cmd in self.config["commands"].values() if cmd["enabled"]])}

## Comandos por Categoría

"""

        # Agrupar comandos por categoría
        for cat_id, cat in self.config["categories"].items():
            if not cat["enabled"]:
                continue

            commands_in_cat = [cmd for cmd in self.config["commands"].values()
                             if cmd["category"] == cat_id and cmd["enabled"]]

            if not commands_in_cat:
                continue

            docs += f"### {cat['name']}\n\n"

            for cmd in commands_in_cat:
                docs += f"#### `{self.config['settings']['prefix']}{cmd['name']}`\n\n"
                docs += f"**Descripción**: {cmd['description']}\n\n"
                docs += f"**Uso**: `{cmd['usage']}`\n\n"
                docs += f"**Permisos**: {cmd['permission']}\n\n"

                if cmd["aliases"]:
                    docs += f"**Aliases**: {', '.join([f'`{alias}`' for alias in cmd['aliases']])}\n\n"

                if cmd["cooldown"] > 0:
                    docs += f"**Cooldown**: {cmd['cooldown']} segundos\n\n"

                docs += "---\n\n"

        return docs

    def generate_html_docs(self):
        """Genera documentación en HTML"""
        html = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.config["bot_info"]["name"]} - Comandos</title>
    <style>
        body {{
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #e8e8e8;
            margin: 0;
            padding: 20px;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 26, 46, 0.9);
            border-radius: 16px;
            padding: 30px;
        }}
        h1 {{ color: #ffd700; text-align: center; }}
        h2 {{ color: #ffd700; border-bottom: 2px solid #ffd700; }}
        h3 {{ color: #00ff88; }}
        .command {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }}
        .command-name {{ color: #ffd700; font-weight: bold; font-size: 1.2em; }}
        .command-usage {{ color: #1e90ff; font-family: monospace; }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .stat-card {{
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 {self.config["bot_info"]["name"]}</h1>
        <p style="text-align: center; font-size: 1.1em;">{self.config["bot_info"]["description"]}</p>

        <div class="stats">
            <div class="stat-card">
                <h3>📊 Total Comandos</h3>
                <p style="font-size: 2em; color: #ffd700;">{len(self.config["commands"])}</p>
            </div>
            <div class="stat-card">
                <h3>✅ Comandos Activos</h3>
                <p style="font-size: 2em; color: #00ff88;">{len([cmd for cmd in self.config["commands"].values() if cmd["enabled"]])}</p>
            </div>
            <div class="stat-card">
                <h3>📂 Categorías</h3>
                <p style="font-size: 2em; color: #1e90ff;">{len(self.config["categories"])}</p>
            </div>
        </div>
"""

        # Agregar comandos por categoría
        for cat_id, cat in self.config["categories"].items():
            if not cat["enabled"]:
                continue

            commands_in_cat = [cmd for cmd in self.config["commands"].values()
                             if cmd["category"] == cat_id and cmd["enabled"]]

            if not commands_in_cat:
                continue

            html += f'<h2>{cat["name"]}</h2>\n'

            for cmd in commands_in_cat:
                html += f'''
        <div class="command">
            <div class="command-name">{self.config["settings"]["prefix"]}{cmd["name"]}</div>
            <p><strong>Descripción:</strong> {cmd["description"]}</p>
            <p><strong>Uso:</strong> <span class="command-usage">{cmd["usage"]}</span></p>
            <p><strong>Permisos:</strong> {cmd["permission"]}</p>
'''
                if cmd["aliases"]:
                    html += f'            <p><strong>Aliases:</strong> {", ".join(cmd["aliases"])}</p>\n'

                if cmd["cooldown"] > 0:
                    html += f'            <p><strong>Cooldown:</strong> {cmd["cooldown"]} segundos</p>\n'

                html += '        </div>\n'

        html += """
    </div>
</body>
</html>"""

        return html

    def generate_complete_bot_code(self):
        """Genera código completo del bot con todos los comandos"""
        return f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
{self.config["bot_info"]["name"]} - Bot Completo
Generado automáticamente por Paimon Command Manager
Versión: {self.config["bot_info"]["version"]}
Autor: {self.config["bot_info"]["author"]}

{self.config["bot_info"]["description"]}
\"\"\"

import twitchio
from twitchio.ext import commands
import json
import os
import asyncio
from datetime import datetime, timedelta

# Configuración del bot
BOT_NICK = 'tu_bot_nick'
TOKEN = 'tu_token_aqui'
CHANNELS = ['tu_canal']

class {self.config["bot_info"]["name"].replace(" ", "")}(commands.Bot):
    def __init__(self):
        super().__init__(
            token=TOKEN,
            prefix='{self.config["settings"]["prefix"]}',
            initial_channels=CHANNELS,
            nick=BOT_NICK
        )

        self.config_file = ".PaimonCommands"
        self.load_config()
        self.cooldowns = {{}}  # Sistema de cooldowns

    def load_config(self):
        \"\"\"Carga la configuración de comandos\"\"\"
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {{"commands": {{}}, "settings": {{"prefix": "{self.config["settings"]["prefix"]}"}}}}
        except Exception as e:
            print(f"Error cargando configuración: {{e}}")
            self.config = {{"commands": {{}}, "settings": {{"prefix": "{self.config["settings"]["prefix"]}"}}}}

    async def event_ready(self):
        print(f'🤖 {{self.nick}} está conectado y listo!')
        print(f'📊 Comandos cargados: {{len(self.config.get("commands", {{}}))}}')

    async def event_message(self, message):
        if message.echo:
            return

        await self.handle_commands(message)

    def check_permission(self, ctx, required_permission):
        \"\"\"Verifica si el usuario tiene los permisos necesarios\"\"\"
        if required_permission == "everyone":
            return True
        elif required_permission == "subscribers":
            return ctx.author.is_subscriber or ctx.author.is_mod or ctx.author.name.lower() == ctx.channel.name.lower()
        elif required_permission == "moderators":
            return ctx.author.is_mod or ctx.author.name.lower() == ctx.channel.name.lower()
        elif required_permission == "broadcaster":
            return ctx.author.name.lower() == ctx.channel.name.lower()
        return False

    def check_cooldown(self, ctx, command_name, cooldown_seconds):
        \"\"\"Verifica el cooldown del comando\"\"\"
        if cooldown_seconds <= 0:
            return True

        user_id = ctx.author.name.lower()
        now = datetime.now()

        if user_id not in self.cooldowns:
            self.cooldowns[user_id] = {{}}

        if command_name in self.cooldowns[user_id]:
            time_diff = (now - self.cooldowns[user_id][command_name]).total_seconds()
            if time_diff < cooldown_seconds:
                return False

        self.cooldowns[user_id][command_name] = now
        return True

# Agregar comandos generados aquí
{self.generate_python_code().split('class GeneratedCommands:')[1].split('def check_permission')[0]}

if __name__ == "__main__":
    bot = {self.config["bot_info"]["name"].replace(" ", "")}()
    bot.run()
"""

    def run(self):
        """Ejecuta la aplicación"""
        # Configurar protocolo de cierre
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Centrar ventana
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")

        print("🤖 Paimon Command Manager iniciado")
        self.root.mainloop()

    def on_closing(self):
        """Maneja el cierre de la aplicación"""
        if self.config["settings"]["auto_save"]:
            self.save_config()

        if messagebox.askokcancel("Salir", "¿Quieres cerrar el Gestor de Comandos?"):
            print("👋 Cerrando Paimon Command Manager")
            self.root.destroy()

def main():
    """Función principal"""
    print("🤖 Iniciando Paimon Bot - Gestor Completo de Comandos")
    print("=" * 60)

    try:
        app = PaimonCommandManager()
        app.run()
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        messagebox.showerror("Error Crítico", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()
