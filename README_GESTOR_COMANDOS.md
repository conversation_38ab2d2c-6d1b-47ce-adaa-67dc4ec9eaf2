# 🤖 Paimon Bot - Gestor Completo de Comandos

## 🌟 Nueva Interfaz Completa para Gestión de Comandos

**¡Ahora puedes configurar TODOS los comandos de tu bot desde una interfaz gráfica!**

### ✨ Características Principales

#### 🎨 Interfaz Gráfica Avanzada
- **Tema oscuro completo** estilo Genshin Impact con tonos profundos
- **Botones mejorados** que no se achican al presionar
- **5 pestañas organizadas**: Comandos, Categorías, Configuración, Permisos y Exportar
- **Controles intuitivos** con validación automática
- **Vista previa en tiempo real** de la configuración

#### 🤖 Gestión Completa de Comandos
- **Crear nuevos comandos** con interfaz visual
- **Editar comandos existentes** con todos sus parámetros
- **Duplicar comandos** para crear variaciones rápidamente
- **Eliminar comandos** con confirmación de seguridad
- **Filtrar y buscar** comandos por categoría o nombre
- **ℹ️ Botón "Comandos Info"** que muestra TODOS los comandos del bot

#### 📂 Sistema de Categorías
- **9 categorías predefinidas**: Niveles GD, Puntos, Sorteos, Juegos, Moderación, Utilidades, Diversión, OBS, Personalizados
- **Crear categorías personalizadas** con colores y nombres únicos
- **Gestionar permisos** por categoría y rol de usuario
- **Organización visual** con colores distintivos

#### 📁 Archivos .paimon Exclusivos
- **Formato .paimon** personalizado para máxima compatibilidad
- **Exportar configuración** con nombre automático basado en el bot
- **Importar solo archivos .paimon** para mayor seguridad
- **Respaldos automáticos** con timestamp

### 🎯 Comandos Preconfigurados

El gestor incluye **todos los comandos existentes** del bot Paimon:

#### 🎮 Comandos de Niveles GD
- **!add** - Agregar nivel a la cola
- **!lvl** - Mostrar siguiente nivel
- **!queue** - Ver cola de niveles
- **!P** - Ver tu posición en la cola
- **!remove** - Remover nivel (mods)
- **!clear** - Limpiar cola (mods)

#### 💰 Sistema de Puntos
- **!points** - Ver tus puntos
- **!daily** - Reclamar puntos diarios
- **!shop** - Ver tienda de puntos
- **!buy** - Comprar items
- **!give** - Dar puntos (mods)

#### 🎁 Sorteos y Rifas
- **!sorteo** - Iniciar sorteo (mods)
- **!participar** - Unirse al sorteo
- **!ganador** - Seleccionar ganador (mods)
- **!sorteoinfo** - Info del sorteo activo

#### 🎲 Juegos y Diversión
- **!8ball** - Bola mágica
- **!roll** - Lanzar dado
- **!flip** - Lanzar moneda
- **!rps** - Piedra, papel o tijera
- **!hug** - Abrazar usuario
- **!slap** - Golpear usuario (broma)
- **!love** - Mostrar amor

#### 🛠️ Utilidades
- **!time** - Hora actual
- **!calc** - Calculadora
- **!translate** - Traducir texto
- **!uptime** - Tiempo de stream
- **!viewers** - Número de viewers

#### 📺 Integración OBS
- **!topactive** - Top usuarios activos
- **!obsinfo** - Info archivos OBS
- **!obscompact** - Versión compacta
- **!obsmini** - Versión mini

### 🔧 Configuración Avanzada

#### ⚙️ Configuración General
- **Nombre del bot** y información básica
- **Prefijo de comandos** personalizable
- **Cooldowns globales** y por usuario
- **Longitud máxima** de mensajes
- **Sensibilidad a mayúsculas**

#### 🔐 Sistema de Permisos
- **everyone** - Todos los usuarios
- **subscribers** - Suscriptores del canal
- **moderators** - Moderadores
- **broadcaster** - Solo el streamer

#### 📤 Exportación Múltiple
- **Archivo .paimon** - Configuración completa
- **Código Python** - Para integración directa
- **Documentación** - HTML y Markdown
- **Bot completo** - Código funcional del bot

### 🚀 Cómo Usar

#### 1. Ejecutar el Gestor
```bash
python paimon_command_manager.py
```

#### 2. Explorar Comandos Existentes
- Haz clic en **"ℹ️ Comandos Info"** para ver todos los comandos
- Navega por las categorías y explora la funcionalidad
- Doble clic en cualquier comando para ver detalles completos

#### 3. Personalizar Comandos
- **Pestaña Comandos**: Crear, editar, duplicar o eliminar
- **Pestaña Categorías**: Organizar comandos por temas
- **Pestaña Configuración**: Ajustar comportamiento del bot
- **Pestaña Permisos**: Controlar quién puede usar qué

#### 4. Exportar Configuración
- **Guardar**: Archivo `paimon_commands.paimon` local
- **Exportar .paimon**: Archivo portable con nombre personalizado
- **Exportar Python**: Código para integrar con tu bot

### 📊 Ventana "Comandos Info"

La nueva ventana de información muestra:

#### 📈 Estadísticas Completas
- Total de comandos configurados
- Comandos activos vs inactivos
- Número de categorías
- Prefijo actual del bot

#### 📋 Lista Detallada
- **Vista por categorías** con separadores visuales
- **Información completa** de cada comando
- **Estados visuales** (✅ activo, ❌ inactivo)
- **Cooldowns y permisos** claramente mostrados

#### 🔍 Funciones Avanzadas
- **Doble clic** para ver detalles completos
- **Exportar lista** a archivo de texto
- **Actualizar** información en tiempo real
- **Editar directo** desde la ventana de info

### 💾 Archivos .paimon

#### 🎯 Ventajas del Formato .paimon
- **Formato exclusivo** para configuraciones de Paimon
- **Compatibilidad garantizada** con el gestor
- **Nombres automáticos** basados en el bot
- **Estructura JSON** fácil de leer y editar

#### 📁 Gestión de Archivos
- **Exportar**: `nombre_del_bot_commands.paimon`
- **Importar**: Solo acepta archivos `.paimon`
- **Respaldos**: Automáticos con timestamp
- **Portabilidad**: Fácil de compartir entre instalaciones

### 🎨 Mejoras Visuales

#### 🖱️ Botones Mejorados
- **No se achican** al hacer clic
- **Padding consistente** para mejor apariencia
- **Efectos hover** suaves y elegantes
- **Colores coherentes** con el tema oscuro

#### 🌙 Tema Oscuro Perfeccionado
- **Contraste optimizado** para mejor legibilidad
- **Colores Genshin Impact** auténticos
- **Transiciones suaves** entre estados
- **Iconos y emojis** perfectamente integrados

### 🔄 Flujo de Trabajo Recomendado

1. **Explorar** → Usar "Comandos Info" para ver qué tienes
2. **Personalizar** → Editar comandos existentes según tus necesidades
3. **Expandir** → Crear nuevos comandos personalizados
4. **Organizar** → Ajustar categorías y permisos
5. **Exportar** → Guardar configuración en archivo .paimon
6. **Integrar** → Usar código Python generado en tu bot

### 🎯 Casos de Uso

#### 🎮 Streamer de Geometry Dash
- Comandos de niveles preconfigurados
- Sistema de puntos para engagement
- Sorteos automáticos para la comunidad

#### 🤖 Bot Personalizado
- Crear comandos únicos para tu canal
- Configurar permisos específicos
- Exportar código para desarrollo

#### 👥 Comunidad
- Compartir configuraciones .paimon
- Documentar comandos para moderadores
- Mantener consistencia entre bots

---

## 🌟 Resultado Final

**¡Tienes un gestor completo y profesional para todos los comandos de tu bot!**

- 🤖 **Interfaz intuitiva** con tema oscuro elegante
- 📋 **Gestión completa** de comandos y categorías
- 📁 **Archivos .paimon** exclusivos y portables
- ℹ️ **Información detallada** de todos los comandos
- 🔧 **Configuración avanzada** con permisos granulares
- 📤 **Exportación múltiple** para máxima flexibilidad

**¡Tu bot Paimon nunca fue tan fácil de configurar y personalizar!** 🌟
