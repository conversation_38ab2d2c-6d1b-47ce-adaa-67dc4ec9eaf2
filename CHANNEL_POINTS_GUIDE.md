# 💎 Guía de Channel Points - Integración con el Bot

## 🎯 **¿Qué son los Channel Points?**

Los Channel Points son puntos que Twitch da automáticamente a los espectadores por:
- Ver el stream
- Seguir el canal
- Suscribirse
- Participar en raids
- Completar predicciones

## 🤖 **Integración con el Bot**

El bot ahora puede responder a recompensas de Channel Points, permitiendo que los espectadores usen sus puntos para interactuar con el stream de formas únicas.

## 🎁 **Recompensas Disponibles**

### **✨ Destacar Mensaje (500 puntos)**
- **Acción**: El próximo mensaje del usuario será destacado
- **Efecto**: `✨🌟 MENSAJE DESTACADO 🌟✨ @usuario: mensaje ✨🌟✨`

### **🚀 Saltarse Cola (1,000 puntos)**
- **Acción**: Mueve el primer nivel del usuario al frente de la cola
- **Requisito**: El usuario debe tener al menos un nivel en la cola

### **👑 Elegir Próximo Nivel (1,500 puntos)**
- **Acción**: Permite al usuario agregar un nivel específico como próximo
- **Uso**: El usuario debe especificar el ID del nivel

### **🎲 Nivel Aleatorio (800 puntos)**
- **Acción**: Selecciona un nivel aleatorio de la cola y lo pone como próximo
- **Requisito**: Debe haber niveles en la cola

### **💰 Ganar Puntos Bot (300 puntos)**
- **Acción**: Otorga 50-150 puntos del bot al usuario
- **Beneficio**: Puntos para usar en la tienda del bot (!shop)

### **🧠 Pregunta Trivia (200 puntos)**
- **Acción**: El bot hace una pregunta de trivia sobre Geometry Dash
- **Efecto**: Pregunta y respuesta mostradas en el chat

### **🎉 Entrada Sorteo Gratis (400 puntos)**
- **Acción**: Agrega automáticamente al usuario al sorteo activo
- **Requisito**: Debe haber un sorteo activo

### **📢 Mensaje Personalizado (600 puntos)**
- **Acción**: El bot envía un mensaje especial del usuario
- **Formato**: `📢 MENSAJE ESPECIAL: [mensaje] - Por @usuario 🌟`

## ⚙️ **Comandos del Bot**

### **Para Usuarios:**
- **`!channelpoints`** - Ver todas las recompensas disponibles y sus costos
- **`!redeem <recompensa>`** - Simular el canje de una recompensa (para pruebas)

### **Para Moderadores:**
- **`!togglechannelpoints`** - Activar/desactivar el sistema
- **`!setrewardcost <recompensa> <costo>`** - Cambiar el costo de una recompensa

## 🔧 **Configuración en Twitch**

### **Paso 1: Crear Recompensas Personalizadas**
1. Ve a tu **Panel de Creador** en Twitch
2. **Configuración** → **Preferencias** → **Recompensas de Channel Points**
3. **Crear nueva recompensa personalizada**

### **Paso 2: Configurar cada Recompensa**

#### **Ejemplo: Destacar Mensaje**
```
Título: Destacar Mensaje
Descripción: Tu próximo mensaje será destacado por el bot
Costo: 500 puntos
Requiere entrada del usuario: No
Límite por stream: Sin límite
Límite por usuario: Sin límite
Tiempo de espera: 30 segundos
```

#### **Ejemplo: Elegir Próximo Nivel**
```
Título: Elegir Próximo Nivel
Descripción: Agrega un nivel específico como próximo (escribe el ID)
Costo: 1500 puntos
Requiere entrada del usuario: Sí
Límite por stream: Sin límite
Límite por usuario: 1 por hora
Tiempo de espera: 300 segundos
```

### **Paso 3: Configurar Alertas**
- **StreamLabs/OBS**: Configurar alertas para mostrar cuando alguien canjea
- **Sonidos**: Agregar sonidos únicos para cada tipo de recompensa

## 🎮 **Ejemplos de Uso**

### **Escenario 1: Usuario quiere destacar mensaje**
1. Usuario canjea "Destacar Mensaje" (500 puntos)
2. Bot marca al usuario internamente
3. Usuario escribe: "¡Este nivel está increíble!"
4. Bot responde: `✨🌟 MENSAJE DESTACADO 🌟✨ @usuario: ¡Este nivel está increíble! ✨🌟✨`

### **Escenario 2: Usuario quiere saltarse la cola**
1. Usuario tiene nivel en posición #5
2. Usuario canjea "Saltarse Cola" (1,000 puntos)
3. Bot mueve su nivel a posición #1
4. Bot confirma: `🚀 @usuario tu nivel 12345678 ha sido movido al frente de la cola!`

### **Escenario 3: Usuario quiere elegir nivel específico**
1. Usuario canjea "Elegir Próximo Nivel" (1,500 puntos)
2. Usuario escribe el ID: "87654321"
3. Bot agrega el nivel al frente de la cola
4. Bot confirma: `👑 @usuario tu nivel 87654321 ha sido agregado como próximo nivel!`

## 📊 **Costos Recomendados**

| Recompensa | Costo Base | Ajuste Sugerido |
|------------|------------|-----------------|
| Destacar Mensaje | 500 | 300-800 |
| Ganar Puntos Bot | 300 | 200-500 |
| Pregunta Trivia | 200 | 100-300 |
| Entrada Sorteo | 400 | 300-600 |
| Mensaje Personalizado | 600 | 400-1000 |
| Nivel Aleatorio | 800 | 600-1200 |
| Saltarse Cola | 1000 | 800-1500 |
| Elegir Próximo Nivel | 1500 | 1000-2500 |

## 🎯 **Consejos para Streamers**

### **Balanceo:**
- **Recompensas baratas** (200-500): Para participación frecuente
- **Recompensas medianas** (500-1000): Para interacciones especiales
- **Recompensas caras** (1000+): Para momentos únicos

### **Límites recomendados:**
- **Destacar Mensaje**: Sin límite (fomenta participación)
- **Saltarse Cola**: 1 por usuario por hora
- **Elegir Nivel**: 1 por usuario por stream
- **Nivel Aleatorio**: 3 por stream total

### **Horarios especiales:**
- **Eventos especiales**: Reducir costos 50%
- **Celebraciones**: Recompensas temporales únicas
- **Fines de semana**: Bonificaciones extra

## 🔄 **Mantenimiento**

### **Comandos útiles:**
- `!channelpoints` - Ver estado actual
- `!setrewardcost destacar 300` - Cambiar costo
- `!togglechannelpoints` - Activar/desactivar

### **Monitoreo:**
- Revisar uso de recompensas semanalmente
- Ajustar costos según participación
- Agregar nuevas recompensas según feedback

¡El sistema de Channel Points hace que tu stream sea más interactivo y da a los espectadores formas únicas de participar! 🎉
