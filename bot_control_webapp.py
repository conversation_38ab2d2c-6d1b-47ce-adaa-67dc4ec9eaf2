import threading
import asyncio
import time

# Global bot state
bot_state = {
    "active": False,
    "restart_requested": False,
    "shutdown_requested": False,
}

# Placeholder for bot thread
bot_thread = None

def run_bot():
    import importlib.util
    import sys
    import os

    module_name = "Paimon_bot_twitch"
    module_path = os.path.join(os.path.dirname(__file__), "Paimon bot twitch.py")

    import importlib.machinery
    loader = importlib.machinery.SourceFileLoader(module_name, module_path)
    bot_module = loader.load_module()
    sys.modules[module_name] = bot_module

    global bot_state
    import asyncio
    while True:
        # Set event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        bot_instance = bot_module.Bot(loop=loop)
        try:
            bot_state["active"] = True
            bot_instance.run()
            # Check for shutdown or restart requests periodically
            while True:
                if bot_state["shutdown_requested"]:
                    bot_instance.restart = False
                    bot_instance.stop()  # Assuming bot has a stop method
                    bot_state["active"] = False
                    break
                if bot_state["restart_requested"]:
                    bot_instance.restart = True
                    bot_state["restart_requested"] = False
                    bot_instance.stop()  # Assuming bot has a stop method
                    break
                time.sleep(1)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            print(f"Error inesperado en el bot: {e}")
        bot_state["active"] = False
        if not bot_instance.restart:
            break
        time.sleep(1)

def start_bot():
    global bot_thread, bot_state
    if bot_thread is None or not bot_thread.is_alive():
        bot_state["shutdown_requested"] = False
        bot_state["restart_requested"] = False
        bot_thread = threading.Thread(target=run_bot, daemon=True)
        bot_thread.start()
        print("Bot started.")
    else:
        print("Bot is already running.")

def restart_bot():
    global bot_state
    if bot_state["active"]:
        bot_state["restart_requested"] = True
        print("Bot restart requested.")
    else:
        print("Bot is not running.")

def shutdown_bot():
    global bot_state
    if bot_state["active"]:
        bot_state["shutdown_requested"] = True
        print("Bot shutdown requested.")
    else:
        print("Bot is not running.")

def print_status():
    print(f"Bot active: {bot_state['active']}")

def main():
    print("Bot Control CLI")
    print("Commands: start, restart, shutdown, status, exit")
    while True:
        cmd = input("Enter command: ").strip().lower()
        if cmd == "start":
            start_bot()
        elif cmd == "restart":
            restart_bot()
        elif cmd == "shutdown":
            shutdown_bot()
        elif cmd == "status":
            print_status()
        elif cmd == "exit":
            if bot_state["active"]:
                print("Please shutdown the bot before exiting.")
            else:
                print("Exiting.")
                break
        else:
            print("Unknown command. Available commands: start, restart, shutdown, status, exit")

if __name__ == "__main__":
    main()
