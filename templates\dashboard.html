<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paimon Bot Dashboard - Genshin Style</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: #e8e8e8;
            overflow: hidden;
            position: relative;
        }

        /* Fondo animado estilo Genshin */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(30, 144, 255, 0.1) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Ventanas flotantes estilo Genshin */
        .floating-window {
            position: absolute;
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 16px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            cursor: move;
            min-width: 280px;
            min-height: 200px;
            resize: both;
            overflow: auto;
        }

        .window-header {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(138, 43, 226, 0.2));
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 14px 14px 0 0;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .window-title {
            font-weight: 600;
            font-size: 14px;
            color: #ffd700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .window-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .minimize { background: #ffd700; }
        .maximize { background: #00ff88; }
        .close { background: #ff6b6b; }

        .control-btn:hover {
            transform: scale(1.2);
            box-shadow: 0 0 8px currentColor;
        }
        
        .window-content {
            padding: 16px;
            height: calc(100% - 45px);
            overflow-y: auto;
        }

        /* Scrollbar personalizado estilo Genshin */
        .window-content::-webkit-scrollbar {
            width: 6px;
        }

        .window-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .window-content::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ffd700, #ff8c00);
            border-radius: 3px;
        }

        .window-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #ffed4e, #ffa500);
        }

        /* Elementos de contenido */
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: rgba(255, 215, 0, 0.4);
            transform: translateY(-2px);
        }

        .stat-label {
            font-size: 13px;
            color: #b8b8b8;
            font-weight: 400;
        }

        .stat-value {
            font-size: 14px;
            color: #ffd700;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .user-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(138, 43, 226, 0.2);
            border-radius: 8px;
            padding: 10px 12px;
            margin: 6px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .user-item:hover {
            background: rgba(138, 43, 226, 0.1);
            border-color: rgba(138, 43, 226, 0.4);
            transform: translateX(4px);
        }

        .user-name {
            color: #e8e8e8;
            font-size: 13px;
            font-weight: 500;
        }

        .user-stats {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #b8b8b8;
        }

        .queue-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(30, 144, 255, 0.2);
            border-radius: 8px;
            padding: 10px 12px;
            margin: 6px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .queue-item:hover {
            background: rgba(30, 144, 255, 0.1);
            border-color: rgba(30, 144, 255, 0.4);
        }

        .queue-position {
            background: linear-gradient(135deg, #1e90ff, #4169e1);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 11px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .queue-info {
            flex: 1;
        }

        .level-id {
            color: #1e90ff;
            font-weight: 600;
            font-size: 13px;
        }

        .level-author {
            color: #b8b8b8;
            font-size: 11px;
            margin-top: 2px;
        }

        /* Botones de control */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 12px;
            z-index: 1000;
        }

        .control-button {
            background: rgba(26, 26, 46, 0.9);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            color: #ffd700;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-button:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: rgba(255, 215, 0, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }
        
        .queue-position {
            background: #ff00ff;
            color: #000000;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .level-info {
            flex-grow: 1;
            margin-left: 15px;
        }
        
        .level-id {
            color: #ffff00;
            font-size: 1.1em;
        }
        
        .level-author {
            color: #ffffff;
            font-size: 0.9em;
        }
        
        .raffle-active {
            background: linear-gradient(145deg, #ff0066, #ff3399);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .online { background: #00ff00; }
        .offline { background: #ff0000; }
        
        /* Indicador de actualización */
        .update-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 11px;
            color: #b8b8b8;
            backdrop-filter: blur(10px);
        }

        /* Efectos especiales */
        .glow-effect {
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
            to { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .floating-window {
                min-width: 250px;
                font-size: 12px;
            }

            .control-panel {
                top: 10px;
                right: 10px;
                gap: 8px;
            }

            .control-button {
                padding: 6px 10px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <!-- Panel de Control -->
    <div class="control-panel">
        <button class="control-button" onclick="toggleAllWindows()">👁️ Mostrar/Ocultar</button>
        <button class="control-button" onclick="resetWindowPositions()">🔄 Resetear</button>
        <button class="control-button" onclick="updateData()">📡 Actualizar</button>
    </div>

    <!-- Ventana de Estadísticas -->
    <div class="floating-window" id="stats-window" style="top: 50px; left: 50px; width: 320px; height: 280px;">
        <div class="window-header">
            <span class="window-title">📊 Estadísticas del Canal</span>
            <div class="window-controls">
                <button class="control-btn minimize" onclick="minimizeWindow('stats-window')"></button>
                <button class="control-btn maximize" onclick="maximizeWindow('stats-window')"></button>
                <button class="control-btn close" onclick="closeWindow('stats-window')"></button>
            </div>
        </div>
        <div class="window-content">
            <div class="stat-item">
                <span class="stat-label">Canal:</span>
                <span class="stat-value" id="channel">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Tiempo Activo:</span>
                <span class="stat-value" id="uptime">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Usuarios Activos:</span>
                <span class="stat-value" id="active-users">-</span>
            </div>
        </div>
    </div>

    <!-- Ventana de Top Chatters -->
    <div class="floating-window" id="chatters-window" style="top: 50px; left: 400px; width: 350px; height: 320px;">
        <div class="window-header">
            <span class="window-title">💬 Top Chatters</span>
            <div class="window-controls">
                <button class="control-btn minimize" onclick="minimizeWindow('chatters-window')"></button>
                <button class="control-btn maximize" onclick="maximizeWindow('chatters-window')"></button>
                <button class="control-btn close" onclick="closeWindow('chatters-window')"></button>
            </div>
        </div>
        <div class="window-content" id="top-chatters">
            <div class="stat-item">
                <span class="stat-label">Cargando...</span>
            </div>
        </div>
    </div>

    <!-- Ventana de Cola de Niveles -->
    <div class="floating-window" id="queue-window" style="top: 400px; left: 50px; width: 380px; height: 350px;">
        <div class="window-header">
            <span class="window-title">🎮 Cola de Niveles</span>
            <div class="window-controls">
                <button class="control-btn minimize" onclick="minimizeWindow('queue-window')"></button>
                <button class="control-btn maximize" onclick="maximizeWindow('queue-window')"></button>
                <button class="control-btn close" onclick="closeWindow('queue-window')"></button>
            </div>
        </div>
        <div class="window-content" id="level-queue">
            <div class="stat-item">
                <span class="stat-label">Cargando...</span>
            </div>
        </div>
    </div>

    <!-- Ventana de Sorteo -->
    <div class="floating-window glow-effect" id="raffle-window" style="top: 400px; left: 450px; width: 300px; height: 200px;">
        <div class="window-header">
            <span class="window-title">🎁 Sorteo Activo</span>
            <div class="window-controls">
                <button class="control-btn minimize" onclick="minimizeWindow('raffle-window')"></button>
                <button class="control-btn maximize" onclick="maximizeWindow('raffle-window')"></button>
                <button class="control-btn close" onclick="closeWindow('raffle-window')"></button>
            </div>
        </div>
        <div class="window-content" id="raffle-info">
            <div class="stat-item">
                <span class="stat-label">Sin sorteo activo</span>
            </div>
        </div>
    </div>

    <!-- Ventana de Top Contributors -->
    <div class="floating-window" id="contributors-window" style="top: 50px; left: 780px; width: 350px; height: 320px;">
        <div class="window-header">
            <span class="window-title">⭐ Top Contributors</span>
            <div class="window-controls">
                <button class="control-btn minimize" onclick="minimizeWindow('contributors-window')"></button>
                <button class="control-btn maximize" onclick="maximizeWindow('contributors-window')"></button>
                <button class="control-btn close" onclick="closeWindow('contributors-window')"></button>
            </div>
        </div>
        <div class="window-content" id="top-contributors">
            <div class="stat-item">
                <span class="stat-label">Cargando...</span>
            </div>
        </div>
    </div>

    <!-- Indicador de actualización -->
    <div class="update-indicator" id="update-indicator">
        Última actualización: Cargando...
    </div>

    <script>
        let updateInterval;
        let isDragging = false;
        let currentWindow = null;
        let offset = { x: 0, y: 0 };

        // Funciones de ventanas flotantes
        function makeWindowDraggable(windowElement) {
            const header = windowElement.querySelector('.window-header');

            header.addEventListener('mousedown', (e) => {
                isDragging = true;
                currentWindow = windowElement;
                offset.x = e.clientX - windowElement.offsetLeft;
                offset.y = e.clientY - windowElement.offsetTop;
                windowElement.style.zIndex = 1000;
            });
        }

        document.addEventListener('mousemove', (e) => {
            if (isDragging && currentWindow) {
                currentWindow.style.left = (e.clientX - offset.x) + 'px';
                currentWindow.style.top = (e.clientY - offset.y) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            currentWindow = null;
        });

        function minimizeWindow(windowId) {
            const window = document.getElementById(windowId);
            window.style.height = '45px';
            window.style.overflow = 'hidden';
        }

        function maximizeWindow(windowId) {
            const window = document.getElementById(windowId);
            window.style.height = 'auto';
            window.style.overflow = 'auto';
        }

        function closeWindow(windowId) {
            const window = document.getElementById(windowId);
            window.style.display = 'none';
        }

        function toggleAllWindows() {
            const windows = document.querySelectorAll('.floating-window');
            windows.forEach(window => {
                window.style.display = window.style.display === 'none' ? 'block' : 'none';
            });
        }

        function resetWindowPositions() {
            const positions = [
                { id: 'stats-window', top: 50, left: 50, width: 320, height: 280 },
                { id: 'chatters-window', top: 50, left: 400, width: 350, height: 320 },
                { id: 'queue-window', top: 400, left: 50, width: 380, height: 350 },
                { id: 'raffle-window', top: 400, left: 450, width: 300, height: 200 },
                { id: 'contributors-window', top: 50, left: 780, width: 350, height: 320 }
            ];

            positions.forEach(pos => {
                const window = document.getElementById(pos.id);
                if (window) {
                    window.style.top = pos.top + 'px';
                    window.style.left = pos.left + 'px';
                    window.style.width = pos.width + 'px';
                    window.style.height = pos.height + 'px';
                    window.style.display = 'block';
                    window.style.overflow = 'auto';
                }
            });
        }

        function updateData() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStats(data);
                    } else {
                        console.error('Error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        }

        function updateStats(data) {
            // Actualizar indicador de tiempo
            document.getElementById('update-indicator').textContent = `Última actualización: ${data.timestamp}`;

            // Actualizar estadísticas generales
            document.getElementById('channel').textContent = data.channel;
            document.getElementById('uptime').textContent = data.uptime;
            document.getElementById('active-users').textContent = data.total_active;

            // Actualizar top chatters
            const chattersContainer = document.getElementById('top-chatters');
            chattersContainer.innerHTML = '';
            data.top_chatters.forEach((user, index) => {
                const userDiv = document.createElement('div');
                userDiv.className = 'user-item';
                userDiv.innerHTML = `
                    <span class="user-name">#${index + 1} ${user.username}</span>
                    <div class="user-stats">
                        <span>💬 ${user.messages}</span>
                        <span>⭐ ${user.points}</span>
                    </div>
                `;
                chattersContainer.appendChild(userDiv);
            });

            // Actualizar cola de niveles
            const queueContainer = document.getElementById('level-queue');
            queueContainer.innerHTML = '';
            if (data.level_queue.length === 0) {
                queueContainer.innerHTML = '<div class="stat-item"><span class="stat-label">Cola vacía</span></div>';
            } else {
                data.level_queue.forEach(level => {
                    const levelDiv = document.createElement('div');
                    levelDiv.className = 'queue-item';
                    levelDiv.innerHTML = `
                        <div class="queue-position">${level.position}</div>
                        <div class="queue-info">
                            <div class="level-id">ID: ${level.level_id}</div>
                            <div class="level-author">Por: ${level.username}</div>
                        </div>
                    `;
                    queueContainer.appendChild(levelDiv);
                });
            }

            // Actualizar sorteo
            const raffleWindow = document.getElementById('raffle-window');
            const raffleInfo = document.getElementById('raffle-info');

            if (data.raffle.active) {
                raffleWindow.classList.add('glow-effect');
                raffleInfo.innerHTML = `
                    <div class="stat-item">
                        <span class="stat-label">Premio:</span>
                        <span class="stat-value">${data.raffle.prize}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Participantes:</span>
                        <span class="stat-value">${data.raffle.participants}</span>
                    </div>
                `;
            } else {
                raffleWindow.classList.remove('glow-effect');
                raffleInfo.innerHTML = '<div class="stat-item"><span class="stat-label">Sin sorteo activo</span></div>';
            }

            // Actualizar top contributors
            const contributorsContainer = document.getElementById('top-contributors');
            contributorsContainer.innerHTML = '';
            data.top_contributors.forEach((user, index) => {
                const userDiv = document.createElement('div');
                userDiv.className = 'user-item';
                userDiv.innerHTML = `
                    <span class="user-name">#${index + 1} ${user.username}</span>
                    <div class="user-stats">
                        <span>⭐ ${user.points}</span>
                        <span>🎮 ${user.levels_submitted}</span>
                    </div>
                `;
                contributorsContainer.appendChild(userDiv);
            });
        }

        // Inicializar ventanas flotantes
        document.addEventListener('DOMContentLoaded', () => {
            const windows = document.querySelectorAll('.floating-window');
            windows.forEach(makeWindowDraggable);

            // Cargar datos iniciales
            updateData();
            updateInterval = setInterval(updateData, 5000); // Actualizar cada 5 segundos
        });

        // Limpiar intervalo al cerrar
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
