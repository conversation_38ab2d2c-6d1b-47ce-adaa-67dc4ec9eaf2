"""
Script para ejecutar el bot con la interfaz web
"""

import subprocess
import time
import threading
import sys
import os

def run_bot():
    """Ejecuta el bot principal"""
    print("🤖 Iniciando bot principal...")
    try:
        # Ejecutar el bot sin la interfaz web
        subprocess.run([sys.executable, "PBot_simple.py"], cwd=os.getcwd())
    except KeyboardInterrupt:
        print("Bot interrumpido por el usuario.")
    except Exception as e:
        print(f"Error ejecutando el bot: {e}")

def run_interface():
    """Ejecuta la interfaz web"""
    print("🌐 Iniciando interfaz web...")
    time.sleep(5)  # Esperar a que el bot se inicie
    try:
        subprocess.run([sys.executable, "standalone_interface.py"], cwd=os.getcwd())
    except KeyboardInterrupt:
        print("Interfaz web interrumpida por el usuario.")
    except Exception as e:
        print(f"Error ejecutando la interfaz web: {e}")

def main():
    """Función principal"""
    print("🚀 Iniciando Paimon Bot con Interfaz Web...")
    print("=" * 50)
    
    # Crear hilos para bot e interfaz
    bot_thread = threading.Thread(target=run_bot, daemon=True)
    interface_thread = threading.Thread(target=run_interface, daemon=True)
    
    # Iniciar hilos
    bot_thread.start()
    interface_thread.start()
    
    try:
        # Mantener el programa corriendo
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Cerrando aplicación...")
        print("Presiona Ctrl+C nuevamente para forzar el cierre.")

if __name__ == "__main__":
    main()
