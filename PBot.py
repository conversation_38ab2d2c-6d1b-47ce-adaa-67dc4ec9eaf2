# Bot de Twitch que guarda niveles por ID y consulta a GDBrowser
# Librerías necesarias: twitchio, aiohttp
# Instalar con: pip install twitchio aiohttp

import aiohttp
import asyncio
import os
import json
import random
import time
import datetime
from twitchio.ext import commands

BOT_NICK = 'guisodepaimon'
TOKEN = 'oauth:fxyp5yny604oheoipw08d6bvnjw0tz'
REFRESH_TOKEN = '8wy84wapv1amjep5rqi8n95r1kdn62iqyhzc2sxj3tbip'
CLIENT_ID = 'gp762nuuoqcoxypju8c569th9wz7q5'
# Para la nueva versión de twitchio - obtén estos valores de tu aplicación de Twitch
# https://dev.twitch.tv/console/apps
CLIENT_SECRET = 'tu_client_secret_aqui'  # Client Secret de tu aplicación
BOT_ID = 'tu_bot_id_aqui'  # User ID del bot (puedes obtenerlo con la API de Twitch)
CHANNELS = ['gabriv4', 'littlekittyqwq', 'FlozWer', 'cbas_ds']

# Configuración de rutas
PERMISSIONS_FILE = "permissions.js"
ACCEPTED_LEVELS_DIR = "accepted_levels"
ACCEPTED_LEVELS_FILE = os.path.join(ACCEPTED_LEVELS_DIR, "all_levels.js")

# Nota: Actualmente el script no implementa lógica para usar REFRESH_TOKEN y CLIENT_ID.
# Se recomienda agregar manejo de renovación de token OAuth si es necesario.

# Lista para guardar niveles con usuario que los agregó (tupla: (level_id, username))
# Cambiado a diccionario para manejar niveles por canal
lista_ids_per_channel = {}

# Estructura para manejar sorteos activos por canal
# Formato: {canal: {"premio": str, "participantes": set, "activo": bool, "inicio": datetime, "duracion": int}}
sorteos_activos = {}

# Sistema de puntos por usuario y canal
# Formato: {canal: {usuario: {"points": int, "last_daily": datetime, "levels_submitted": int, "achievements": set}}}
user_data = {}

# Estadísticas de niveles por canal
# Formato: {canal: {"total_levels": int, "levels_by_user": {usuario: int}, "levels_by_difficulty": {dificultad: int}}}
level_stats = {}

# Datos de configuración del canal
# Formato: {canal: {"slowmode": int, "emoteonly": bool, "followersonly": int, "filters": {...}}}
channel_config = {}

# Lista de palabras prohibidas por canal
banned_words = {}

# Recordatorios y eventos
# Formato: {canal: {"reminders": [...], "events": [...], "schedule": {...}}}
channel_events = {}

# Sistema de actividad de usuarios para OBS
# Formato: {canal: {usuario: {"messages": int, "last_activity": datetime, "points": int, "levels_sent": int}}}
user_activity = {}

# Configuración de archivos OBS
OBS_FILES_DIR = "obs_files"
ACTIVE_VIEWERS_FILE = "active_viewers.txt"
TOP_CONTRIBUTORS_FILE = "top_contributors.txt"
STREAM_STATS_FILE = "stream_stats.txt"
CURRENT_LEVEL_FILE = "current_level.txt"
NEXT_LEVELS_FILE = "next_levels.txt"
COMPACT_VIEWERS_FILE = "compact_viewers.txt"
COMPACT_STATS_FILE = "compact_stats.txt"
MINI_VIEWERS_FILE = "mini_viewers.txt"
MINI_STATS_FILE = "mini_stats.txt"

# Configuración de traducción automática por canal
# Formato: {canal: {"auto_translate": bool, "target_language": str, "min_length": int}}
translation_config = {}

# Sistema de recompensas con Channel Points
# Formato: {canal: {"channel_points_rewards": {reward_id: {"name": str, "cost": int, "action": str}}}}
channel_points_config = {}

# Configuración de recompensas por defecto
DEFAULT_CHANNEL_REWARDS = {
    "highlight_message": {"name": "Destacar Mensaje", "cost": 500, "action": "highlight"},
    "skip_queue": {"name": "Saltarse Cola", "cost": 1000, "action": "skip_queue"},
    "choose_level": {"name": "Elegir Próximo Nivel", "cost": 1500, "action": "choose_level"},
    "random_level": {"name": "Nivel Aleatorio", "cost": 800, "action": "random_level"},
    "add_points": {"name": "Ganar Puntos Bot", "cost": 300, "action": "add_bot_points"},
    "trivia_question": {"name": "Pregunta Trivia", "cost": 200, "action": "trivia"},
    "sorteo_entry": {"name": "Entrada Sorteo Gratis", "cost": 400, "action": "free_raffle"},
    "custom_message": {"name": "Mensaje Personalizado", "cost": 600, "action": "custom_message"}
}

# Diccionario de detección de idiomas básico
language_patterns = {
    "en": ["the", "and", "you", "that", "was", "for", "are", "with", "his", "they", "this", "have", "from", "not", "been", "more", "her", "were", "said", "each", "which", "their", "time", "will", "about", "would", "there", "could", "other", "after", "first", "well", "water", "very", "what", "know", "get", "through", "back", "much", "good", "new", "write", "our", "me", "man", "too", "any", "day", "same", "right", "look", "think", "also", "around", "another", "came", "come", "work", "three", "must", "because", "does", "part"],
    "fr": ["le", "de", "et", "un", "il", "être", "et", "en", "avoir", "que", "pour", "dans", "ce", "son", "une", "sur", "avec", "ne", "se", "pas", "tout", "plus", "par", "grand", "en", "une", "être", "et", "de", "il", "avoir", "ne", "je", "son", "que", "se", "qui", "ce", "dans", "en", "du", "elle", "au", "de", "ce", "le", "pour", "sont", "une", "ou", "et", "son", "mais", "faire", "leur", "bien", "où", "sans", "peut", "tous", "après", "ainsi", "donc", "cela", "être", "faire", "aller", "pouvoir", "vouloir"],
    "de": ["der", "die", "und", "in", "den", "von", "zu", "das", "mit", "sich", "des", "auf", "für", "ist", "im", "dem", "nicht", "ein", "eine", "als", "auch", "es", "an", "werden", "aus", "er", "hat", "dass", "sie", "nach", "wird", "bei", "einer", "um", "am", "sind", "noch", "wie", "einem", "über", "einen", "so", "zum", "war", "haben", "nur", "oder", "aber", "vor", "zur", "bis", "mehr", "durch", "man", "sein", "wurde", "sei", "in", "ich", "mit", "das", "der", "und"],
    "it": ["il", "di", "che", "e", "la", "per", "un", "in", "con", "non", "una", "su", "le", "da", "questo", "come", "ma", "se", "del", "ci", "sono", "lo", "nel", "dalla", "sua", "tutto", "lei", "mio", "fatto", "così", "lui", "mia", "fare", "era", "solo", "anche", "due", "vita", "tempo", "vero", "mai", "dire", "grande", "casa", "altro", "bene", "dove", "stesso", "cosa", "tanto", "uomo", "anni", "ancora", "senza", "cose", "suoi", "essere", "molto", "stato", "persona", "anno", "fare", "anche", "grande", "bene", "qui", "quello", "paese", "questa", "quello", "suoi", "cose"],
    "pt": ["o", "de", "a", "e", "do", "da", "em", "um", "para", "é", "com", "não", "uma", "os", "no", "se", "na", "por", "mais", "as", "dos", "como", "mas", "foi", "ao", "ele", "das", "tem", "à", "seu", "sua", "ou", "ser", "quando", "muito", "há", "nos", "já", "está", "eu", "também", "só", "pelo", "pela", "até", "isso", "ela", "entre", "era", "depois", "sem", "mesmo", "aos", "ter", "seus", "quem", "nas", "me", "esse", "eles", "estão", "você", "tinha", "foram", "essa", "num", "nem", "suas", "meu", "às", "minha", "têm", "numa", "pelos", "elas", "havia", "seja", "qual", "será", "nós", "tenho", "lhe", "deles", "essas", "esses", "pelas", "este", "fosse", "dele"],
    "ru": ["в", "и", "не", "на", "я", "быть", "с", "он", "а", "как", "по", "но", "они", "к", "у", "его", "за", "от", "мы", "из", "вы", "так", "же", "для", "или", "что", "этот", "до", "бы", "если", "уже", "когда", "здесь", "об", "нет", "при", "всё", "еще", "можно", "после", "без", "году", "два", "год", "во", "время", "очень", "своей", "то", "тоже", "где", "дом", "лучше", "людей", "если", "жизни", "никто", "больше", "день", "тем", "чем", "нас", "новый", "том", "под", "человек", "вас", "теперь", "дело", "жить", "система", "лет", "нее", "группы", "развития", "процесс", "тысяч", "многие", "решение", "обычно"]
}

class Bot(commands.Bot):

    def __init__(self):
        # Verificar si tenemos credenciales reales
        if CLIENT_SECRET == 'tu_client_secret_aqui' or BOT_ID == 'tu_bot_id_aqui':
            print("⚠️  CREDENCIALES NO CONFIGURADAS")
            print("=" * 50)
            print("🔧 Para usar PBot.py necesitas configurar:")
            print("1. CLIENT_SECRET - Obtén de https://dev.twitch.tv/console/apps")
            print("2. BOT_ID - User ID del bot (usa herramientas online)")
            print("")
            print("🚀 ALTERNATIVA FÁCIL:")
            print("Usa el bot integrado que no necesita configuración:")
            print("python paimon_bot_integrated.py")
            print("=" * 50)
            raise ValueError("Credenciales no configuradas. Usa paimon_bot_integrated.py o configura CLIENT_SECRET y BOT_ID")

        # Intentar inicializar con la nueva API de twitchio
        try:
            super().__init__(
                token=TOKEN,
                client_id=CLIENT_ID,
                client_secret=CLIENT_SECRET,
                bot_id=BOT_ID,
                prefix='!',
                initial_channels=CHANNELS
            )
        except TypeError:
            # Si falla, intentar con la API antigua
            try:
                super().__init__(
                    token=TOKEN,
                    prefix='!',
                    initial_channels=CHANNELS
                )
            except Exception as e:
                print(f"❌ Error inicializando bot: {e}")
                print("💡 Soluciones posibles:")
                print("1. Actualiza twitchio: pip install --upgrade twitchio")
                print("2. Configura CLIENT_SECRET y BOT_ID en las variables del archivo")
                print("3. Usa la versión integrada: python paimon_bot_integrated.py")
                raise
        self.restart = False
        self.dmnoff = False  # Toggle to prohibit demon levels
        self.accept_levels = True  # Toggle to accept or not accept levels
        self.streamer = CHANNELS[0].lower()  # Se asume que el streamer es el dueño del primer canal
        self.permissions = set()
        self.load_permissions()
        self.last_level_data = None  # Para guardar datos del último nivel mostrado
        self.max_levels_per_user = None  # Número máximo de niveles que un usuario puede agregar (None = sin límite)
        self.start_time = datetime.datetime.now()  # Tiempo de inicio del bot
        self.active_chatters = set()  # Usuarios activos en el chat

        # Inicializar configuración de traducción por canal
        for channel in CHANNELS:
            channel_lower = channel.lower()
            if channel_lower not in translation_config:
                translation_config[channel_lower] = {
                    "auto_translate": True,  # Activado por defecto
                    "target_language": "es",  # Traducir a español por defecto
                    "min_length": 5  # Reducido a 5 caracteres para ser más sensible
                }

            # Inicializar configuración de Channel Points
            if channel_lower not in channel_points_config:
                channel_points_config[channel_lower] = {
                    "enabled": True,
                    "rewards": DEFAULT_CHANNEL_REWARDS.copy()
                }

        # Crear directorio para niveles aceptados si no existe
        if not os.path.exists(ACCEPTED_LEVELS_DIR):
            os.makedirs(ACCEPTED_LEVELS_DIR)

        # Crear directorio para archivos OBS si no existe
        if not os.path.exists(OBS_FILES_DIR):
            os.makedirs(OBS_FILES_DIR)

        # Crear directorio para datos de la interfaz web
        if not os.path.exists("data"):
            os.makedirs("data")

        # Inicializar interfaz web
        self.web_interface = None

    def load_permissions(self):
        """Carga permisos desde archivo"""
        if os.path.exists(PERMISSIONS_FILE):
            try:
                with open(PERMISSIONS_FILE, "r", encoding="utf-8") as f:
                    content = f.read()
                    start = content.find('[')
                    end = content.rfind(']')
                    if start != -1 and end != -1:
                        json_str = content[start:end+1]
                        perms = json.loads(json_str)
                        self.permissions = set(perms)
                        print(f"Permisos cargados: {self.permissions}")
            except Exception as e:
                print(f"Error leyendo {PERMISSIONS_FILE}: {e}")
                self.permissions = {self.streamer}
        else:
            # Inicializar con el streamer y flozwer
            self.permissions = {self.streamer, "flozwer"}
            self.save_permissions()

    def save_permissions(self):
        """Guarda permisos en archivo"""
        perms_list = sorted(self.permissions)
        try:
            with open(PERMISSIONS_FILE, "w", encoding="utf-8") as f:
                f.write("export const permissions = ")
                json.dump(perms_list, f, ensure_ascii=False, indent=2)
                f.write(";")
            print(f"Permisos guardados: {perms_list}")
        except Exception as e:
            print(f"Error escribiendo {PERMISSIONS_FILE}: {e}")

    def validate_level_id(self, level_id):
        """Valida que el ID del nivel sea numérico y válido"""
        try:
            level_id_int = int(level_id)
            return level_id_int > 0
        except ValueError:
            return False

    def has_permission(self, ctx):
        """Verifica si el usuario tiene permisos para comandos especiales"""
        username = ctx.author.name.lower()
        return username == self.streamer or username in self.permissions

    def detect_language(self, text):
        """Detecta el idioma de un texto basado en palabras comunes (mejorado)"""
        text_lower = text.lower().strip()

        # Limpiar texto de caracteres especiales pero mantener acentos
        import re
        text_clean = re.sub(r'[^\w\sáéíóúàèìòùâêîôûäëïöüñç]', ' ', text_lower)
        words = text_clean.split()

        # Reducir requisito mínimo para ser más sensible
        if len(words) < 2:  # Cambiar de 3 a 2 palabras mínimo
            return None

        language_scores = {}

        # Patrones adicionales específicos para detección rápida
        quick_patterns = {
            "en": ["lol", "omg", "wtf", "gg", "wp", "nice", "cool", "awesome", "yeah", "nope", "the", "and", "you", "that", "this", "with", "have", "from", "they", "what", "know", "good", "very", "right", "think", "also", "because"],
            "fr": ["mdr", "ptdr", "oui", "non", "merci", "salut", "bonjour", "ça", "très", "bien", "comment", "où", "quand", "pourquoi", "le", "de", "et", "un", "il", "être", "avoir", "que", "pour", "dans", "ce", "son", "une", "sur", "avec"],
            "de": ["lol", "ja", "nein", "danke", "hallo", "wie", "geht", "gut", "sehr", "der", "die", "und", "in", "den", "von", "zu", "das", "mit", "sich", "auf", "für", "ist", "nicht", "ein", "eine", "auch", "werden"],
            "it": ["lol", "sì", "no", "grazie", "ciao", "come", "stai", "bene", "molto", "il", "di", "che", "e", "la", "per", "un", "in", "con", "non", "una", "su", "le", "da", "questo", "come", "ma", "se"],
            "pt": ["kkkk", "rsrs", "sim", "não", "obrigado", "oi", "como", "vai", "bem", "muito", "o", "de", "a", "e", "do", "da", "em", "um", "para", "é", "com", "não", "uma", "os", "no", "se", "na"],
            "ru": ["лол", "да", "нет", "спасибо", "привет", "как", "дела", "хорошо", "очень", "в", "и", "не", "на", "я", "быть", "он", "с", "что", "а", "по", "это", "она", "этот", "к", "но", "они", "мы"]
        }

        # Primero verificar con patrones rápidos (más peso)
        for lang, patterns in quick_patterns.items():
            score = 0
            for word in words:
                if word in patterns:
                    score += 2  # Doble peso para patrones rápidos

            if len(words) > 0:
                language_scores[lang] = score / len(words)

        # Luego verificar con patrones completos
        for lang, patterns in language_patterns.items():
            current_score = language_scores.get(lang, 0)
            additional_score = 0

            for word in words:
                if word in patterns:
                    additional_score += 1

            if len(words) > 0:
                language_scores[lang] = current_score + (additional_score / len(words))

        if not language_scores:
            return None

        # Obtener el idioma con mayor puntuación
        best_lang = max(language_scores, key=language_scores.get)
        best_score = language_scores[best_lang]

        # Usar umbral configurable o por defecto
        default_threshold = 0.15
        if best_score >= default_threshold:
            return best_lang

        return None

    def translate_text(self, text, source_lang, target_lang):
        """Traduce texto usando un diccionario básico"""
        # Diccionario de traducciones básicas
        translations = {
            "en_es": {
                "hello": "hola", "hi": "hola", "hey": "hola", "bye": "adiós", "goodbye": "adiós",
                "thank you": "gracias", "thanks": "gracias", "please": "por favor",
                "yes": "sí", "no": "no", "good": "bueno", "bad": "malo", "great": "genial",
                "level": "nivel", "game": "juego", "play": "jugar", "win": "ganar",
                "lose": "perder", "easy": "fácil", "hard": "difícil", "demon": "demonio",
                "nice": "genial", "cool": "genial", "awesome": "increíble", "amazing": "increíble",
                "how": "cómo", "what": "qué", "when": "cuándo", "where": "dónde",
                "why": "por qué", "who": "quién", "can": "puedo", "help": "ayuda",
                "stream": "stream", "chat": "chat", "follow": "seguir", "viewer": "espectador",
                "subscribe": "suscribirse", "like": "me gusta", "love": "amor",
                "i": "yo", "you": "tú", "he": "él", "she": "ella", "we": "nosotros",
                "they": "ellos", "this": "esto", "that": "eso", "here": "aquí", "there": "allí",
                "now": "ahora", "today": "hoy", "tomorrow": "mañana", "yesterday": "ayer",
                "time": "tiempo", "day": "día", "night": "noche", "morning": "mañana",
                "afternoon": "tarde", "evening": "noche", "week": "semana", "month": "mes",
                "year": "año", "new": "nuevo", "old": "viejo", "big": "grande", "small": "pequeño",
                "fast": "rápido", "slow": "lento", "first": "primero", "last": "último",
                "best": "mejor", "worst": "peor", "fun": "divertido", "boring": "aburrido",
                "difficult": "difícil", "impossible": "imposible", "possible": "posible",
                "try": "intentar", "again": "otra vez", "more": "más", "less": "menos",
                "very": "muy", "really": "realmente", "maybe": "tal vez", "sure": "seguro",
                "ok": "ok", "okay": "ok", "fine": "bien", "perfect": "perfecto",
                # Gaming y chat específico
                "lol": "jajaja", "omg": "dios mío", "wtf": "qué diablos", "gg": "buena partida",
                "wp": "bien jugado", "rip": "descansa en paz", "epic": "épico", "fail": "fallo",
                "noob": "novato", "pro": "profesional", "skill": "habilidad", "beat": "vencer",
                "complete": "completar", "finish": "terminar", "retry": "reintentar",
                "practice": "práctica", "player": "jugador", "winner": "ganador", "loser": "perdedor",
                # Verbos comunes adicionales
                "want": "quiero", "need": "necesito", "have": "tengo", "get": "obtener",
                "give": "dar", "take": "tomar", "make": "hacer", "do": "hacer", "go": "ir",
                "come": "venir", "see": "ver", "look": "mirar", "watch": "ver", "know": "saber",
                "think": "pensar", "feel": "sentir", "understand": "entender", "learn": "aprender",
                "work": "trabajar", "play": "jugar", "stop": "parar", "start": "empezar",
                "open": "abrir", "close": "cerrar", "find": "encontrar", "lose": "perder",
                "win": "ganar", "buy": "comprar", "sell": "vender", "wait": "esperar",
                "run": "correr", "walk": "caminar", "jump": "saltar", "sit": "sentarse",
                "stand": "pararse", "eat": "comer", "drink": "beber", "sleep": "dormir",
                "wake": "despertar", "read": "leer", "write": "escribir", "speak": "hablar",
                "talk": "hablar", "listen": "escuchar", "hear": "oír", "ask": "preguntar",
                "answer": "responder", "tell": "contar", "say": "decir", "call": "llamar",
                "help": "ayudar", "try": "intentar", "use": "usar", "move": "mover",
                "turn": "girar", "change": "cambiar", "stay": "quedarse", "leave": "irse",
                "arrive": "llegar", "return": "regresar", "remember": "recordar", "forget": "olvidar"
            },
            "fr_es": {
                "bonjour": "hola", "salut": "hola", "au revoir": "adiós", "merci": "gracias",
                "oui": "sí", "non": "no", "bon": "bueno", "mauvais": "malo",
                "niveau": "nivel", "jeu": "juego", "jouer": "jugar", "gagner": "ganar",
                "perdre": "perder", "facile": "fácil", "difficile": "difícil",
                "génial": "genial", "cool": "genial", "incroyable": "increíble",
                "comment": "cómo", "quoi": "qué", "quand": "cuándo", "où": "dónde",
                "pourquoi": "por qué", "qui": "quién", "aide": "ayuda"
            },
            "de_es": {
                "hallo": "hola", "tschüss": "adiós", "danke": "gracias", "bitte": "por favor",
                "ja": "sí", "nein": "no", "gut": "bueno", "schlecht": "malo",
                "level": "nivel", "spiel": "juego", "spielen": "jugar", "gewinnen": "ganar",
                "verlieren": "perder", "einfach": "fácil", "schwer": "difícil",
                "toll": "genial", "cool": "genial", "fantastisch": "increíble",
                "wie": "cómo", "was": "qué", "wann": "cuándo", "wo": "dónde",
                "warum": "por qué", "wer": "quién", "hilfe": "ayuda"
            },
            "it_es": {
                "ciao": "hola", "arrivederci": "adiós", "grazie": "gracias", "prego": "por favor",
                "sì": "sí", "no": "no", "buono": "bueno", "cattivo": "malo",
                "livello": "nivel", "gioco": "juego", "giocare": "jugar", "vincere": "ganar",
                "perdere": "perder", "facile": "fácil", "difficile": "difícil",
                "fantastico": "genial", "cool": "genial", "incredibile": "increíble",
                "come": "cómo", "cosa": "qué", "quando": "cuándo", "dove": "dónde",
                "perché": "por qué", "chi": "quién", "aiuto": "ayuda"
            },
            "pt_es": {
                "olá": "hola", "oi": "hola", "tchau": "adiós", "obrigado": "gracias",
                "sim": "sí", "não": "no", "bom": "bueno", "ruim": "malo",
                "nível": "nivel", "jogo": "juego", "jogar": "jugar", "ganhar": "ganar",
                "perder": "perder", "fácil": "fácil", "difícil": "difícil",
                "legal": "genial", "cool": "genial", "incrível": "increíble",
                "como": "cómo", "o que": "qué", "quando": "cuándo", "onde": "dónde",
                "por que": "por qué", "quem": "quién", "ajuda": "ayuda"
            },
            "ru_es": {
                "привет": "hola", "пока": "adiós", "спасибо": "gracias", "пожалуйста": "por favor",
                "да": "sí", "нет": "no", "хорошо": "bueno", "плохо": "malo",
                "уровень": "nivel", "игра": "juego", "играть": "jugar", "выиграть": "ganar",
                "проиграть": "perder", "легко": "fácil", "трудно": "difícil",
                "круто": "genial", "классно": "genial", "невероятно": "increíble",
                "как": "cómo", "что": "qué", "когда": "cuándo", "где": "dónde",
                "почему": "por qué", "кто": "quién", "помощь": "ayuda"
            }
        }

        translation_key = f"{source_lang}_{target_lang}"
        if translation_key not in translations:
            return None

        text_lower = text.lower()
        translation_dict = translations[translation_key]

        # Primero buscar frases completas
        for phrase, translation in translation_dict.items():
            if " " in phrase and phrase in text_lower:
                text_lower = text_lower.replace(phrase, translation)

        # Luego traducir palabras individuales
        words = text_lower.split()
        translated_words = []
        translated_count = 0

        for word in words:
            # Limpiar puntuación
            clean_word = word.strip('.,!?;:()')
            if clean_word in translation_dict:
                translated_words.append(translation_dict[clean_word])
                translated_count += 1
            else:
                translated_words.append(word)

        # Solo devolver traducción si se tradujo al menos 25% de las palabras
        if len(words) > 0 and translated_count / len(words) >= 0.25:
            return " ".join(translated_words)

        return None

    def update_user_activity(self, channel, username):
        """Actualiza la actividad de un usuario"""
        if channel not in user_activity:
            user_activity[channel] = {}

        if username not in user_activity[channel]:
            user_activity[channel][username] = {
                "messages": 0,
                "last_activity": datetime.datetime.now(),
                "points": 0,
                "levels_sent": 0
            }

        user_activity[channel][username]["messages"] += 1
        user_activity[channel][username]["last_activity"] = datetime.datetime.now()

        # Obtener puntos del sistema de puntos si existe
        if channel in user_data and username in user_data[channel]:
            user_activity[channel][username]["points"] = user_data[channel][username]["points"]
            user_activity[channel][username]["levels_sent"] = user_data[channel][username]["levels_submitted"]

        # Guardar datos cada 10 mensajes (para no sobrecargar)
        if user_activity[channel][username]["messages"] % 10 == 0:
            self.save_web_data()

    # ==================== SISTEMA DE CHANNEL POINTS ====================

    def process_channel_points_reward(self, channel, username, reward_name, user_input=None):
        """Procesa una recompensa de Channel Points"""
        if channel not in channel_points_config or not channel_points_config[channel]["enabled"]:
            return False

        rewards = channel_points_config[channel]["rewards"]
        reward_info = None

        # Buscar la recompensa por nombre
        for reward_id, reward_data in rewards.items():
            if reward_data["name"].lower() == reward_name.lower():
                reward_info = reward_data
                break

        if not reward_info:
            return False

        action = reward_info["action"]

        # Procesar según el tipo de acción
        if action == "highlight":
            return self.handle_highlight_message(channel, username)
        elif action == "skip_queue":
            return self.handle_skip_queue(channel, username)
        elif action == "choose_level":
            return self.handle_choose_level(channel, username, user_input)
        elif action == "random_level":
            return self.handle_random_level(channel, username)
        elif action == "add_bot_points":
            return self.handle_add_bot_points(channel, username)
        elif action == "trivia":
            return self.handle_trivia_question(channel, username)
        elif action == "free_raffle":
            return self.handle_free_raffle_entry(channel, username)
        elif action == "custom_message":
            return self.handle_custom_message(channel, username, user_input)

        return False

    def handle_highlight_message(self, channel, username):
        """Maneja la recompensa de destacar mensaje"""
        # Marcar al usuario para destacar su próximo mensaje
        if channel not in user_data:
            user_data[channel] = {}

        self.init_user_data(channel, username)
        user_data[channel][username]["highlight_next"] = True

        return f"✨ @{username} tu próximo mensaje será destacado! ✨"

    def handle_skip_queue(self, channel, username):
        """Maneja la recompensa de saltarse la cola"""
        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            return f"@{username} no hay niveles en la cola para saltar."

        # Buscar niveles del usuario en la cola
        user_levels = []
        for i, (level_id, level_user) in enumerate(lista_ids_per_channel[channel]):
            if level_user == username:
                user_levels.append((i, level_id))

        if not user_levels:
            return f"@{username} no tienes niveles en la cola."

        # Mover el primer nivel del usuario al frente
        first_level_index, level_id = user_levels[0]
        level_data = lista_ids_per_channel[channel].pop(first_level_index)
        lista_ids_per_channel[channel].insert(0, level_data)

        return f"🚀 @{username} tu nivel {level_id} ha sido movido al frente de la cola!"

    def handle_choose_level(self, channel, username, level_id):
        """Maneja la recompensa de elegir próximo nivel"""
        if not level_id:
            return f"@{username} debes especificar un ID de nivel."

        try:
            level_id = int(level_id)
        except ValueError:
            return f"@{username} ID de nivel inválido."

        # Agregar el nivel al frente de la cola
        if channel not in lista_ids_per_channel:
            lista_ids_per_channel[channel] = []

        lista_ids_per_channel[channel].insert(0, (level_id, username))

        return f"👑 @{username} tu nivel {level_id} ha sido agregado como próximo nivel!"

    def handle_random_level(self, channel, username):
        """Maneja la recompensa de nivel aleatorio"""
        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            return f"@{username} no hay niveles en la cola para aleatorizar."

        # Seleccionar un nivel aleatorio de la cola
        random_level = random.choice(lista_ids_per_channel[channel])
        level_id, level_user = random_level

        # Mover al frente
        lista_ids_per_channel[channel].remove(random_level)
        lista_ids_per_channel[channel].insert(0, random_level)

        return f"🎲 @{username} nivel aleatorio seleccionado: {level_id} (por @{level_user}) - ¡Ahora es el próximo!"

    def handle_add_bot_points(self, channel, username):
        """Maneja la recompensa de ganar puntos del bot"""
        self.init_user_data(channel, username)

        bonus_points = random.randint(50, 150)
        user_data[channel][username]["points"] += bonus_points

        return f"💰 @{username} has ganado {bonus_points} puntos del bot! Total: {user_data[channel][username]['points']} puntos"

    def handle_trivia_question(self, channel, username):
        """Maneja la recompensa de pregunta trivia"""
        questions = [
            {"q": "¿Quién creó Geometry Dash?", "a": "RobTop"},
            {"q": "¿Cuál es el primer nivel oficial?", "a": "Stereo Madness"},
            {"q": "¿En qué año se lanzó GD?", "a": "2013"},
            {"q": "¿Cuántos niveles oficiales hay?", "a": "21"},
            {"q": "¿Cuál es el nivel demon más fácil?", "a": "The Nightmare"}
        ]

        question = random.choice(questions)
        return f"🧠 TRIVIA para @{username}: {question['q']} (Respuesta: {question['a']})"

    def handle_free_raffle_entry(self, channel, username):
        """Maneja la recompensa de entrada gratis al sorteo"""
        if channel not in sorteos_activos or not sorteos_activos[channel]["activo"]:
            return f"@{username} no hay sorteo activo en este momento."

        sorteo = sorteos_activos[channel]
        if username in sorteo["participantes"]:
            return f"@{username} ya estás participando en el sorteo."

        sorteo["participantes"].add(username)
        return f"🎉 @{username} has sido agregado al sorteo gratis! Total participantes: {len(sorteo['participantes'])}"

    def handle_custom_message(self, channel, username, message):
        """Maneja la recompensa de mensaje personalizado"""
        if not message:
            message = f"¡Mensaje especial de @{username}!"

        return f"📢 MENSAJE ESPECIAL: {message} - Por @{username} 🌟"

    def generate_obs_files(self, channel):
        """Genera archivos para OBS con información de espectadores activos"""
        if channel not in user_activity:
            return

        now = datetime.datetime.now()

        # Filtrar usuarios activos (últimos 30 minutos)
        active_users = {}
        for username, data in user_activity[channel].items():
            time_diff = now - data["last_activity"]
            if time_diff.total_seconds() <= 1800:  # 30 minutos
                active_users[username] = data

        # Generar archivo de espectadores más activos por mensajes
        self.generate_active_viewers_file(active_users)

        # Generar archivo de top contribuidores (por puntos/niveles)
        self.generate_top_contributors_file(active_users)

        # Generar archivo de estadísticas del stream
        self.generate_stream_stats_file(channel, active_users)

        # Generar archivo de próximos niveles
        self.generate_next_levels_file(channel)

        # Generar versiones compactas
        self.generate_compact_files(channel, active_users)

    def generate_active_viewers_file(self, active_users):
        """Genera archivo minimalista con espectadores activos"""
        # Ordenar por número de mensajes
        sorted_users = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)

        content = "TOP CHAT\n"

        for i, (username, data) in enumerate(sorted_users[:5], 1):  # Solo top 5
            messages = data["messages"]
            # Estado simple
            time_diff = datetime.datetime.now() - data["last_activity"]
            minutes_ago = int(time_diff.total_seconds() / 60)
            status = "●" if minutes_ago <= 10 else "○"

            content += f"{i}. {status} {username} ({messages})\n"

        # Escribir archivo
        file_path = os.path.join(OBS_FILES_DIR, ACTIVE_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo OBS de espectadores activos: {e}")

    def generate_top_contributors_file(self, active_users):
        """Genera archivo minimalista con top contribuidores"""
        # Ordenar por puntos primero, luego por niveles enviados
        sorted_users = sorted(active_users.items(),
                            key=lambda x: (x[1]["points"], x[1]["levels_sent"]),
                            reverse=True)

        content = "TOP PUNTOS\n"

        for i, (username, data) in enumerate(sorted_users[:5], 1):  # Solo top 5
            points = data["points"]
            levels = data["levels_sent"]

            content += f"{i}. {username} ({points}pts, {levels}lvl)\n"

        # Escribir archivo
        file_path = os.path.join(OBS_FILES_DIR, TOP_CONTRIBUTORS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo OBS de top contribuidores: {e}")

    def generate_stream_stats_file(self, channel, active_users):
        """Genera archivo minimalista con estadísticas del stream"""
        now = datetime.datetime.now()
        uptime = now - self.start_time

        # Calcular estadísticas
        total_active = len(active_users)
        total_levels_queue = len(lista_ids_per_channel.get(channel, []))

        # Formatear uptime
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60

        if uptime.days > 0:
            uptime_str = f"{uptime.days}d {hours}h {minutes}m"
        else:
            uptime_str = f"{hours}h {minutes}m"

        # Verificar sorteo activo
        sorteo_count = 0
        if channel in sorteos_activos and sorteos_activos[channel]["activo"]:
            sorteo_count = len(sorteos_activos[channel]["participantes"])

        content = f"STREAM INFO\n"
        content += f"Tiempo: {uptime_str}\n"
        content += f"Activos: {total_active}\n"
        content += f"Cola: {total_levels_queue} niveles\n"
        if sorteo_count > 0:
            content += f"Sorteo: {sorteo_count} participantes\n"

        # Escribir archivo
        file_path = os.path.join(OBS_FILES_DIR, STREAM_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo OBS de estadísticas: {e}")

    def generate_next_levels_file(self, channel):
        """Genera archivo minimalista con próximos niveles"""
        if channel not in lista_ids_per_channel:
            levels_queue = []
        else:
            levels_queue = lista_ids_per_channel[channel]

        content = "PRÓXIMOS\n"

        if not levels_queue:
            content += "Cola vacía\n"
            content += "Usa !add <id>\n"
        else:
            for i, (level_id, username) in enumerate(levels_queue[:5], 1):  # Solo 5 niveles
                content += f"{i}. {level_id} ({username})\n"

        # Escribir archivo
        file_path = os.path.join(OBS_FILES_DIR, NEXT_LEVELS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo OBS de próximos niveles: {e}")

    def generate_compact_obs_files(self, channel):
        """Genera versiones compactas de los archivos OBS"""
        if channel not in user_activity:
            return

        # Obtener usuarios activos
        active_users = {}
        now = datetime.datetime.now()
        for username, data in user_activity[channel].items():
            time_diff = now - data["last_activity"]
            if time_diff.total_seconds() <= 1800:  # 30 minutos
                active_users[username] = data

        # Archivo compacto de viewers
        top_viewers = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)[:3]

        compact_viewers = "TOP 3\n"
        for i, (username, data) in enumerate(top_viewers, 1):
            compact_viewers += f"{i}. {username} ({data['messages']})\n"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(compact_viewers)
        except Exception as e:
            print(f"Error escribiendo archivo compacto de viewers: {e}")

        # Archivo compacto de stats
        uptime = now - self.start_time
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60
        queue_count = len(lista_ids_per_channel.get(channel, []))

        compact_stats = f"Tiempo: {hours}h {minutes}m\n"
        compact_stats += f"Activos: {len(active_users)}\n"
        compact_stats += f"Cola: {queue_count}\n"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(compact_stats)
        except Exception as e:
            print(f"Error escribiendo archivo compacto de stats: {e}")

    def generate_mini_obs_files(self, channel):
        """Genera versiones mini (ultra compactas) de los archivos OBS"""
        if channel not in user_activity:
            return

        # Obtener usuarios activos
        active_users = {}
        now = datetime.datetime.now()
        for username, data in user_activity[channel].items():
            time_diff = now - data["last_activity"]
            if time_diff.total_seconds() <= 1800:  # 30 minutos
                active_users[username] = data

        # Archivo mini de viewers (solo top 2)
        top_viewers = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)[:2]

        mini_viewers = ""
        for i, (username, data) in enumerate(top_viewers, 1):
            mini_viewers += f"{i}. {username}\n"

        file_path = os.path.join(OBS_FILES_DIR, MINI_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(mini_viewers)
        except Exception as e:
            print(f"Error escribiendo archivo mini de viewers: {e}")

        # Archivo mini de stats (solo lo esencial)
        uptime = now - self.start_time
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60
        queue_count = len(lista_ids_per_channel.get(channel, []))

        mini_stats = f"{hours}h {minutes}m\n"
        mini_stats += f"{len(active_users)} activos\n"
        mini_stats += f"{queue_count} cola\n"

        file_path = os.path.join(OBS_FILES_DIR, MINI_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(mini_stats)
        except Exception as e:
            print(f"Error escribiendo archivo mini de stats: {e}")

    def generate_current_level_file(self, level_data):
        """Genera archivo con información del nivel actual"""
        content = "╔══════════════════════════════════════════════════════╗\n"
        content += "║               🎯 NIVEL ACTUAL 🎯                    ║\n"
        content += "║                🔥 NOW PLAYING 🔥                    ║\n"
        content += "╚══════════════════════════════════════════════════════╝\n\n"

        if not level_data:
            content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n\n"
            content += "              💫 NINGÚN NIVEL ACTIVO 💫\n\n"
            content += "    ▓▓ 🎮 ¡ESPERANDO PRÓXIMO NIVEL! 🎮 ▓▓\n"
            content += "    ▒▒ ⚡ ¡USA !lvl PARA MOSTRAR! ⚡ ▒▒\n"
            content += "    ░░ 🌟 ¡ENVÍA NIVELES CON !add! 🌟 ░░\n\n"
            content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n"
        else:
            content += "    🔥 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 🔥\n\n"

            level_id = level_data.get("id", "?")
            name = level_data.get("name", "Desconocido")
            author = level_data.get("author", "Desconocido")
            difficulty = level_data.get("difficulty", "?")
            stars = level_data.get("stars", "?")
            likes = level_data.get("likes", 0)
            downloads = level_data.get("downloads", 0)

            content += f"    👑 ▓▓▓ INFORMACIÓN DEL NIVEL ▓▓▓\n"
            content += f"       🎯 ID: {level_id}\n"
            content += f"       📝 NOMBRE: {name}\n"
            content += f"       👤 AUTOR: {author}\n"
            content += f"       ⚡ DIFICULTAD: {difficulty}\n"
            content += f"       ⭐ ESTRELLAS: {stars}\n"
            content += f"    ═══════════════════════════════════════════════\n\n"

            content += f"    📊 ▓▓▓ ESTADÍSTICAS ▓▓▓\n"
            content += f"       👍 LIKES: {likes:,}\n"
            content += f"       ⬇️ DESCARGAS: {downloads:,}\n"

            # Barra de popularidad basada en likes
            popularity = min(likes // 100, 20) if likes > 0 else 0
            pop_bar = "█" * popularity + "░" * (20 - popularity)
            content += f"       🔥 POPULARIDAD: [{pop_bar}]\n"
            content += f"    ═══════════════════════════════════════════════\n\n"

            # Mensaje motivacional según dificultad
            if "demon" in difficulty.lower():
                motivation = "👹 ¡DESAFÍO EXTREMO! ¡TÚ PUEDES! 👹"
            elif "hard" in difficulty.lower():
                motivation = "💪 ¡NIVEL DIFÍCIL! ¡DALE CAÑA! 💪"
            elif "normal" in difficulty.lower():
                motivation = "⚡ ¡BUEN RITMO! ¡SIGUE ASÍ! ⚡"
            else:
                motivation = "🌟 ¡DISFRUTA EL NIVEL! 🌟"

            content += f"         {motivation}\n\n"
            content += "    🔥 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 🔥\n"

        content += "         🎊 ¡BUENA SUERTE EN EL NIVEL! 🎊\n"
        content += "           💖 ¡DISFRUTA JUGANDO! 💖\n"

        # Escribir archivo
        file_path = os.path.join(OBS_FILES_DIR, CURRENT_LEVEL_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo OBS de nivel actual: {e}")

    def generate_compact_files(self, channel, active_users):
        """Genera versiones compactas para pantallas pequeñas"""
        self.generate_compact_obs_files(channel)
        self.generate_mini_obs_files(channel)

    def generate_mini_files(self, channel, active_users):
        """Genera archivos ultra compactos (mini)"""
        self.generate_mini_viewers_file(active_users)
        self.generate_mini_stats_file(channel, active_users)

    def generate_mini_viewers_file(self, active_users):
        """Genera archivo ultra compacto de espectadores"""
        sorted_users = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)

        content = "🔥 TOP 3 🔥\n"

        for i, (username, data) in enumerate(sorted_users[:3], 1):
            messages = data["messages"]
            time_diff = datetime.datetime.now() - data["last_activity"]
            minutes_ago = int(time_diff.total_seconds() / 60)

            if minutes_ago == 0:
                status = "🟢"
            elif minutes_ago <= 5:
                status = "🟡"
            else:
                status = "🔴"

            content += f"{i}.{status}{username[:6]} {messages}\n"

        file_path = os.path.join(OBS_FILES_DIR, MINI_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo mini viewers: {e}")

    def generate_mini_stats_file(self, channel, active_users):
        """Genera archivo ultra compacto de estadísticas"""
        now = datetime.datetime.now()
        uptime = now - self.start_time
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60

        total_active = len(active_users)
        total_levels_queue = len(lista_ids_per_channel.get(channel, []))

        content = "📊 LIVE\n"
        content += f"⏰ {hours}h{minutes}m\n"
        content += f"👥 {total_active}\n"
        content += f"🎮 {total_levels_queue}\n"

        if channel in sorteos_activos and sorteos_activos[channel]["activo"]:
            sorteo = sorteos_activos[channel]
            participantes = len(sorteo["participantes"])
            content += f"🎉 {participantes}\n"

        file_path = os.path.join(OBS_FILES_DIR, MINI_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo mini stats: {e}")

    def generate_compact_viewers_file(self, active_users):
        """Genera archivo súper compacto de espectadores activos"""
        sorted_users = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)

        content = "🔥 TOP CHAT 🔥\n"

        for i, (username, data) in enumerate(sorted_users[:4], 1):
            messages = data["messages"]
            time_diff = datetime.datetime.now() - data["last_activity"]
            minutes_ago = int(time_diff.total_seconds() / 60)

            if minutes_ago == 0:
                status = "🟢"
            elif minutes_ago <= 5:
                status = "🟡"
            else:
                status = "🔴"

            content += f"{i}.{status}{username[:8]:<8} {messages}msg\n"

        content += "💖 ¡Gracias!"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo compacto de viewers: {e}")

    def generate_compact_stats_file(self, channel, active_users):
        """Genera archivo súper compacto de estadísticas"""
        now = datetime.datetime.now()
        uptime = now - self.start_time
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60

        total_active = len(active_users)
        total_messages = sum(data["messages"] for data in active_users.values())
        total_levels_queue = len(lista_ids_per_channel.get(channel, []))

        content = "📊 STREAM 📊\n"
        content += f"⏰ {hours}h {minutes}m\n"
        content += f"👥 {total_active} activos\n"
        content += f"💬 {total_messages} msgs\n"
        content += f"🎮 {total_levels_queue} niveles\n"

        if channel in sorteos_activos and sorteos_activos[channel]["activo"]:
            sorteo = sorteos_activos[channel]
            participantes = len(sorteo["participantes"])
            content += f"🎉 Sorteo: {participantes}\n"

        content += "🚀 ¡En vivo!"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"Error escribiendo archivo compacto de stats: {e}")

    @commands.command(name='dmnoff')
    async def dmnoff_cmd(self, ctx):
        """Activa la prohibición de niveles demonios"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        self.dmnoff = True
        await ctx.send("Prohibición de niveles demonios activada. No se aceptarán niveles Hard Demon, Insane Demon ni Extreme Demon.")

    @commands.command(name='dmnon')
    async def dmnon_cmd(self, ctx):
        """Desactiva la prohibición de niveles demonios"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        self.dmnoff = False
        await ctx.send("Prohibición de niveles demonios desactivada. Se aceptan todos los niveles.")

    @commands.command(name='off')
    async def shutdown(self, ctx):
        """Apaga el bot"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        await ctx.send("Apagando el bot... Hasta luego!")
        try:
            await self.close()
        except asyncio.CancelledError:
            pass

    @commands.command(name='rs')
    async def restart(self, ctx):
        """Reinicia el bot"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        await ctx.send("Reiniciando el bot...")
        self.restart = True
        try:
            await self.close()
        except asyncio.CancelledError:
            pass

    @commands.command(name='rl')
    async def set_max_levels(self, ctx):
        """Establece el límite máximo de niveles por usuario"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !rl <número máximo de niveles por usuario>")
            return

        try:
            max_levels = int(parts[1])
            if max_levels < 1:
                await ctx.send(f"@{ctx.author.name} el número debe ser mayor que 0.")
                return
        except ValueError:
            await ctx.send(f"@{ctx.author.name} por favor ingresa un número válido.")
            return

        self.max_levels_per_user = max_levels
        await ctx.send(f"@{ctx.author.name} el límite máximo de niveles por usuario ha sido establecido a {max_levels}.")

    @commands.command(name='add')
    async def agregar_id(self, ctx):
        """Agrega un nivel a la lista del canal"""
        if not self.accept_levels:
            await ctx.send(f"@{ctx.author.name} actualmente no se están aceptando niveles.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !add <id>")
            return

        level_id = parts[1]
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        # Validar ID del nivel
        if not self.validate_level_id(level_id):
            await ctx.send(f"@{ctx.author.name} el ID del nivel debe ser un número válido.")
            return

        # Inicializar la lista para el canal si no existe
        if channel not in lista_ids_per_channel:
            lista_ids_per_channel[channel] = []

        # Verifica si el usuario ha alcanzado el límite de niveles permitidos
        if self.max_levels_per_user is not None:
            user_levels_count = sum(1 for (_, usr) in lista_ids_per_channel[channel] if usr == user)
            if user_levels_count >= self.max_levels_per_user:
                await ctx.send(f"@{ctx.author.name} has alcanzado el límite de {self.max_levels_per_user} niveles permitidos.")
                return

        # Verifica si el nivel ya fue agregado por el mismo usuario
        if any(lvl_id == level_id and usr == user for (lvl_id, usr) in lista_ids_per_channel[channel]):
            await ctx.send(f"@{ctx.author.name} el nivel {level_id} ya fue agregado por ti a la lista.")
            return

        # Verifica si el ID existe en GDBrowser antes de agregarlo
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://gdbrowser.com/api/level/{level_id}") as resp:
                    if resp.status != 200:
                        await ctx.send(f"@{ctx.author.name} el nivel con ID {level_id} no existe en GDBrowser.")
                        return
                    data = await resp.json()
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error al verificar el nivel: {e}")
            return

        # Check demon prohibition if dmnoff is True
        if self.dmnoff:
            difficulty = data.get("difficulty", "").lower()
            prohibited = ["hard demon", "insane demon", "extreme demon"]
            if any(d in difficulty for d in prohibited):
                await ctx.send(f"@{ctx.author.name} no se están tomando niveles demonios (Hard Demon, Insane Demon, Extreme Demon).")
                return

        # Agregar nivel a la lista interna
        lista_ids_per_channel[channel].append((level_id, user))

        # Actualizar estadísticas
        if channel not in level_stats:
            level_stats[channel] = {"total_levels": 0, "levels_by_user": {}, "levels_by_difficulty": {}}

        level_stats[channel]["total_levels"] += 1
        level_stats[channel]["levels_by_user"][user] = level_stats[channel]["levels_by_user"].get(user, 0) + 1

        difficulty = data.get("difficulty", "Unknown").lower()
        level_stats[channel]["levels_by_difficulty"][difficulty] = level_stats[channel]["levels_by_difficulty"].get(difficulty, 0) + 1

        # Dar puntos por enviar nivel
        self.init_user_data(channel, user)
        points_earned = 10  # Puntos base por enviar nivel
        if "demon" in difficulty:
            points_earned = 25  # Más puntos por niveles demon

        user_data[channel][user]["points"] += points_earned
        user_data[channel][user]["levels_submitted"] += 1

        # Preparar objeto nivel para guardar en archivo JS con la estructura específica
        current_time = datetime.datetime.now()
        timestamp = current_time.strftime('%Y-%m-%d %H:%M:%S') + f".{random.randint(100000, 999999)}"

        level_obj = {
            "id": level_id,
            "name": data.get("name", ""),
            "creator": data.get("author", ""),
            "difficulty": data.get("difficulty", ""),
            "stars": str(data.get("stars", 0)),
            "downloads": str(data.get("downloads", 0)),
            "likes": str(data.get("likes", 0)),
            "song": data.get("songName", ""),
            "length": data.get("length", "XL"),  # Default a XL si no está disponible
            "url": f"https://gdbrowser.com/{level_id}",
            "requested_by": {
                "user_id": str(abs(hash(user)) % 1000000000000000000),  # Generar ID único basado en username
                "username": user,
                "display_name": ctx.author.display_name or user.title()
            },
            "requested_at": timestamp,
            "server_id": str(abs(hash(channel)) % 1000000000000000000),  # Generar ID único basado en canal
            "server_name": channel
        }

        # Ruta del archivo JS para el canal
        filename = f"{channel}_levels.js"

        # Leer niveles existentes del archivo JS si existe
        levels_list = []
        if os.path.exists(filename):
            try:
                with open(filename, "r", encoding="utf-8") as f:
                    content = f.read()
                    # Extraer JSON del archivo JS (asumiendo export const levels = [...];)
                    start = content.find('[')
                    end = content.rfind(']')
                    if start != -1 and end != -1:
                        json_str = content[start:end+1]
                        levels_list = json.loads(json_str)
            except Exception as e:
                print(f"Error leyendo {filename}: {e}")

        # Agregar nuevo nivel a la lista
        levels_list.append(level_obj)

        # Escribir de nuevo el archivo JS con la lista actualizada
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write("export const levels = ")
                json.dump(levels_list, f, ensure_ascii=False, indent=2)
                f.write(";")
        except Exception as e:
            print(f"Error escribiendo {filename}: {e}")

        await ctx.send(f"@{ctx.author.name} tu nivel {level_id} fue agregado a la lista. +{points_earned} puntos! 💰 Total: {user_data[channel][user]['points']} puntos")

    @commands.command(name='lvl')
    async def mostrar_nivel(self, ctx):
        """Muestra el siguiente nivel en la lista"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("No hay niveles en la lista.")
            return

        level_id, submitter_user = lista_ids_per_channel[channel].pop(0)

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://gdbrowser.com/api/level/{level_id}") as resp:
                    if resp.status != 200:
                        await ctx.send(f"No se pudo obtener información del nivel {level_id}.")
                        return
                    data = await resp.json()
        except Exception as e:
            await ctx.send(f"Error al obtener información del nivel {level_id}: {e}")
            return

        # Extraer información del nivel
        nombre = data.get("name", "Desconocido")
        autor = data.get("author", "Desconocido")
        estrellas = data.get("stars", "?")
        dificultad = data.get("difficulty", "?")
        downloads = data.get("downloads", 0)
        likes = data.get("likes", 0)

        # Guardar datos del último nivel mostrado
        self.last_level_data = {
            "id": level_id,
            "name": nombre,
            "author": autor,
            "stars": estrellas,
            "difficulty": dificultad,
            "downloads": downloads,
            "likes": likes
        }

        # Generar archivo OBS del nivel actual
        self.generate_current_level_file(self.last_level_data)

        # Actualizar archivo de próximos niveles
        self.generate_next_levels_file(channel)

        # El indicador Send se basa en si el nivel existe (status 200)
        send_indicator = "Send🟢"

        await ctx.send(f"ID: {level_id} | Nivel: {nombre} | Autor: {autor} | ⭐ {estrellas} | Dificultad: {dificultad} | 👍 {likes} | ⬇️ {downloads} | {send_indicator} | Nivel enviado por: @{submitter_user}")

    @commands.command(name='grabi')
    async def grabi_cmd(self, ctx):
        await ctx.send("gabri ya apaga porfavor")

    @commands.command(name='kitty')
    async def kitty_cmd(self, ctx):
        await ctx.send("kitty te amo")

    @commands.command(name='sendcheck')
    async def send_check(self, ctx):
        """Verifica si un nivel está disponible en GDBrowser"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso correcto: !sendcheck <id_nivel>")
            return

        level_id = parts[1]

        # Validar ID del nivel
        if not self.validate_level_id(level_id):
            await ctx.send(f"@{ctx.author.name} el ID del nivel debe ser un número válido.")
            return

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://gdbrowser.com/api/level/{level_id}") as resp:
                    if resp.status == 200:
                        await ctx.send(f"@{ctx.author.name} el nivel {level_id} está disponible en GDBrowser.")
                    else:
                        await ctx.send(f"@{ctx.author.name} el nivel {level_id} NO está disponible en GDBrowser.")
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error al verificar el nivel: {e}")

    @commands.command(name='Ac')
    async def save_last_level(self, ctx):
        """Guarda el último nivel mostrado en el archivo de niveles aceptados"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        if not self.last_level_data:
            await ctx.send(f"@{ctx.author.name} no hay un nivel mostrado recientemente para guardar.")
            return

        # Parse optional importance argument
        parts = ctx.message.content.split()
        importance = None
        if len(parts) > 1:
            try:
                importance_val = int(parts[1])
                if 1 <= importance_val <= 100:
                    importance = importance_val
                else:
                    await ctx.send(f"@{ctx.author.name} el valor de importancia debe estar entre 1 y 100.")
                    return
            except ValueError:
                await ctx.send(f"@{ctx.author.name} el valor de importancia debe ser un número entero entre 1 y 100.")
                return

        # Read existing saved levels from the file
        existing_data = []
        if os.path.exists(ACCEPTED_LEVELS_FILE):
            try:
                with open(ACCEPTED_LEVELS_FILE, "r", encoding="utf-8") as f:
                    content = f.read()
                    start = content.find('[')
                    end = content.rfind(']')
                    if start != -1 and end != -1:
                        json_str = content[start:end+1]
                        existing_data = json.loads(json_str)
            except Exception as e:
                await ctx.send(f"@{ctx.author.name} error al leer el archivo existente: {e}")
                return

        # Ensure existing_data is a list
        if not isinstance(existing_data, list):
            existing_data = [existing_data]

        level_id = self.last_level_data.get("id")
        user = ctx.author.name

        # Check if this user already saved this level
        for entry in existing_data:
            if entry.get("id") == level_id:
                senders = entry.get("Send")
                if isinstance(senders, list):
                    if user in senders:
                        await ctx.send(f"@{ctx.author.name} ya guardaste este nivel anteriormente.")
                        return
                    else:
                        senders.append(user)
                else:
                    if senders == user:
                        await ctx.send(f"@{ctx.author.name} ya guardaste este nivel anteriormente.")
                        return
                    else:
                        senders = [senders, user]
                entry["Send"] = senders
                if importance is not None:
                    existing_importance = entry.get("importance", 0)
                    if importance > existing_importance:
                        entry["importance"] = importance
                break
        else:
            # Level not found, add new entry with unique incremental ID
            new_entry = self.last_level_data.copy()
            if importance is not None:
                new_entry["importance"] = importance
            new_entry["Send"] = [user]

            # Generate unique incremental ID starting from 0
            existing_ids = [e.get("uniqueId", -1) for e in existing_data if isinstance(e.get("uniqueId", None), int)]
            next_id = max(existing_ids) + 1 if existing_ids else 0
            new_entry["uniqueId"] = next_id

            existing_data.append(new_entry)

        # Save updated data back to the file
        try:
            with open(ACCEPTED_LEVELS_FILE, "w", encoding="utf-8") as f:
                f.write("export const levels = ")
                json.dump(existing_data, f, ensure_ascii=False, indent=4)
                f.write(";")
            if importance is not None:
                await ctx.send(f"@{ctx.author.name} el último nivel mostrado ha sido guardado en el archivo con importancia {importance}.")
            else:
                await ctx.send(f"@{ctx.author.name} el último nivel mostrado ha sido guardado en el archivo.")
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} ocurrió un error al guardar el archivo: {e}")

    @commands.command(name='per')
    async def add_permission(self, ctx):
        """Otorga permisos especiales a un usuario"""
        # Solo el streamer puede otorgar permisos
        if ctx.author.name.lower() != self.streamer:
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando. (Comando solo para streamer)")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !per <usuario>")
            return

        user_to_add = parts[1].lower()
        if user_to_add in self.permissions:
            await ctx.send(f"@{ctx.author.name} el usuario {user_to_add} ya tiene permiso.")
            return

        self.permissions.add(user_to_add)
        self.save_permissions()  # Guardar cambios persistentemente
        await ctx.send(f"@{ctx.author.name} se ha otorgado permiso a {user_to_add}.")

    @commands.command(name='dper')
    async def remove_permission(self, ctx):
        """Revoca permisos especiales de un usuario"""
        # Solo el streamer puede revocar permisos
        if ctx.author.name.lower() != self.streamer:
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando. (Comando solo para streamer)")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !dper <usuario>")
            return

        user_to_remove = parts[1].lower()
        if user_to_remove not in self.permissions:
            await ctx.send(f"@{ctx.author.name} el usuario {user_to_remove} no tiene permiso.")
            return

        # No permitir que el streamer se quite permisos a sí mismo
        if user_to_remove == self.streamer:
            await ctx.send(f"@{ctx.author.name} no puedes quitarte permisos a ti mismo.")
            return

        self.permissions.remove(user_to_remove)
        self.save_permissions()  # Guardar cambios persistentemente
        await ctx.send(f"@{ctx.author.name} se ha revocado el permiso a {user_to_remove}.")

    @commands.command(name='list')
    async def list_permissions(self, ctx):
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        if not self.permissions:
            await ctx.send("No hay usuarios con permisos especiales.")
            return

        usuarios = ', '.join(sorted(self.permissions))
        await ctx.send(f"Usuarios con permiso: {usuarios}")

    @commands.command(name='Ro')
    async def accept_levels(self, ctx):
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        self.accept_levels = True
        await ctx.send("Ahora se están aceptando niveles.")

    @commands.command(name='Rc')
    async def reject_levels(self, ctx):
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return
        self.accept_levels = False
        await ctx.send("Ya no se están aceptando niveles.")

    @commands.command(name='P')
    async def position_in_queue(self, ctx):
        """Muestra la posición de los niveles del usuario en la lista"""
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel:
            await ctx.send(f"@{ctx.author.name} no tienes niveles en la lista.")
            return

        positions = [i+1 for i, (_, usr) in enumerate(lista_ids_per_channel[channel]) if usr == user]
        if not positions:
            await ctx.send(f"@{ctx.author.name} no tienes niveles en la lista.")
            return
        pos_str = ', '.join(str(pos) for pos in positions)
        await ctx.send(f"@{ctx.author.name} tus niveles están en las posiciones: {pos_str}")

    @commands.command(name='com')
    async def commands_list(self, ctx):
        """Muestra la lista de comandos disponibles"""
        # Dividir en múltiples mensajes para no exceder 500 caracteres

        # Mensaje 1: Comandos principales
        msg1 = (
            "📋 COMANDOS PRINCIPALES:\n"
            "!add <id> - Agrega nivel | !lvl - Siguiente nivel | !queue - Ver cola\n"
            "!P - Tu posición | !stats [usuario] - Estadísticas | !top - Top usuarios\n"
            "!points - Ver puntos | !daily - Puntos diarios | !shop - Tienda"
        )
        await ctx.send(msg1)

        # Mensaje 2: Sorteos y juegos
        msg2 = (
            "🎉 SORTEOS: !sorteo <premio> | !participar | !ganador | !sorteoinfo\n"
            "🎮 JUEGOS: !8ball <pregunta> | !roll [num] | !flip | !rps <opción>\n"
            "😄 DIVERSIÓN: !hug <user> | !slap <user> | !love <user> | !fact"
        )
        await ctx.send(msg2)

        # Mensaje 3: Utilidades y OBS
        msg3 = (
            "📊 INFO: !uptime | !viewers | !chatters | !socials\n"
            "🛠️ UTILIDADES: !time [zona] | !calc <operación> | !paimon\n"
            "📁 OBS: !topactive | !obsinfo | !obscompact | !obsmini"
        )
        await ctx.send(msg3)

        # Mensaje 4: Moderación y avanzado
        msg4 = (
            "🌐 TRADUCCIÓN: !translate <idioma> <texto> | !tr <texto>\n"
            "💎 CHANNEL POINTS: !channelpoints | !redeem <recompensa>\n"
            "⚙️ MODS: !remove <pos> | !clear | !skip | !give <user> <points>"
        )
        await ctx.send(msg4)

    @commands.command(name='help')
    async def help_basic(self, ctx):
        """Muestra comandos básicos más importantes"""
        help_text = (
            "🆘 COMANDOS BÁSICOS:\n"
            "!add <id> - Enviar nivel | !P - Tu posición | !points - Ver puntos\n"
            "!daily - Puntos diarios | !participar - Unirse a sorteo\n"
            "!com - Ver todos los comandos | !channelpoints - Recompensas"
        )
        await ctx.send(help_text)

    async def event_ready(self):
        print(f"{BOT_NICK} está listo y conectado a Twitch!")
        print(f"Canales: {CHANNELS}")
        print(f"Permisos iniciales: {self.permissions}")

        # Generar archivos OBS iniciales
        for channel in CHANNELS:
            self.generate_initial_obs_files(channel.lower())
        print(f"Archivos OBS inicializados en: {OBS_FILES_DIR}/")

        # La interfaz web se ejecutará por separado
        print("🤖 Bot iniciado. La interfaz web se puede ejecutar por separado.")

        # Guardar datos iniciales para la interfaz web
        self.save_web_data()

    def save_web_data(self):
        """Guarda datos para la interfaz web"""
        try:
            # Convertir datetime a string para JSON
            def serialize_datetime(obj):
                if isinstance(obj, datetime.datetime):
                    return obj.isoformat()
                return obj

            # Preparar datos para serialización
            web_user_activity = {}
            for channel, users in user_activity.items():
                web_user_activity[channel] = {}
                for username, data in users.items():
                    web_user_activity[channel][username] = {
                        "messages": data.get("messages", 0),
                        "last_activity": serialize_datetime(data.get("last_activity", datetime.datetime.now())),
                        "points": data.get("points", 0),
                        "levels_sent": data.get("levels_sent", 0)
                    }

            # Guardar archivos JSON
            with open("data/user_activity.json", "w", encoding="utf-8") as f:
                json.dump(web_user_activity, f, ensure_ascii=False, indent=2)

            with open("data/user_data.json", "w", encoding="utf-8") as f:
                json.dump(user_data, f, ensure_ascii=False, indent=2)

            with open("data/lista_ids.json", "w", encoding="utf-8") as f:
                json.dump(lista_ids_per_channel, f, ensure_ascii=False, indent=2)

            # Convertir sets a listas para JSON
            web_sorteos = {}
            for channel, sorteo_data in sorteos_activos.items():
                web_sorteos[channel] = {
                    "activo": sorteo_data.get("activo", False),
                    "premio": sorteo_data.get("premio", ""),
                    "participantes": list(sorteo_data.get("participantes", set())),
                    "inicio": serialize_datetime(sorteo_data.get("inicio", datetime.datetime.now())),
                    "duracion": sorteo_data.get("duracion", 0)
                }

            with open("data/sorteos.json", "w", encoding="utf-8") as f:
                json.dump(web_sorteos, f, ensure_ascii=False, indent=2)

            with open("data/channel_points.json", "w", encoding="utf-8") as f:
                json.dump(channel_points_config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"Error guardando datos para interfaz web: {e}")

    def generate_initial_obs_files(self, channel):
        """Genera archivos OBS iniciales cuando el bot se inicia"""
        # Archivo de espectadores activos inicial súper decorado
        initial_content = "╔══════════════════════════════════════════════════╗\n"
        initial_content += "║          🔥 ESPECTADORES MÁS ACTIVOS 🔥          ║\n"
        initial_content += "║                ⭐ HALL OF FAME ⭐                ║\n"
        initial_content += "╚══════════════════════════════════════════════════╝\n\n"
        initial_content += "    ✨ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ✨\n\n"
        initial_content += "              🌟 ESPERANDO ACTIVIDAD 🌟\n\n"
        initial_content += "    ▓▓ 💬 ¡ESCRIBE EN EL CHAT PARA APARECER! 💬 ▓▓\n"
        initial_content += "    ▒▒ 🎮 ¡ENVÍA NIVELES PARA GANAR PUNTOS! 🎮 ▒▒\n"
        initial_content += "    ░░ 🎉 ¡PARTICIPA EN SORTEOS Y JUEGOS! 🎉 ░░\n\n"
        initial_content += "         ⭐ ¡SÉ PARTE DE LA COMUNIDAD! ⭐\n\n"
        initial_content += "    ✨ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ✨\n"
        initial_content += "         🎊 ¡EL STREAM ESTÁ COMENZANDO! 🎊\n"
        initial_content += "              💖 ¡BIENVENIDOS! 💖\n"

        file_path = os.path.join(OBS_FILES_DIR, ACTIVE_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(initial_content)
        except Exception as e:
            print(f"Error creando archivo inicial de espectadores: {e}")

        # Archivo de top contribuidores inicial súper decorado
        contrib_content = "╔═══════════════════════════════════════════════╗\n"
        contrib_content += "║           ⭐ TOP CONTRIBUIDORES ⭐            ║\n"
        contrib_content += "║              🏆 LEGENDS 🏆                   ║\n"
        contrib_content += "╚═══════════════════════════════════════════════╝\n\n"
        contrib_content += "    💎 ◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆ 💎\n\n"
        contrib_content += "              🌟 ESPERANDO HÉROES 🌟\n\n"
        contrib_content += "    ▓▓▓ 🎮 ¡ENVÍA NIVELES DE GD! 🎮 ▓▓▓\n"
        contrib_content += "    ▒▒▒ 💰 ¡GANA PUNTOS DIARIOS! 💰 ▒▒▒\n"
        contrib_content += "    ░░░ 🏆 ¡COMPRA EN LA TIENDA! 🏆 ░░░\n\n"
        contrib_content += "         👑 ¡CONVIÉRTETE EN LEYENDA! 👑\n\n"
        contrib_content += "              🔥 COMANDOS ÚTILES 🔥\n"
        contrib_content += "         💫 !add <id> - Enviar nivel 💫\n"
        contrib_content += "         ⭐ !daily - Puntos diarios ⭐\n"
        contrib_content += "         🌟 !shop - Ver tienda 🌟\n\n"
        contrib_content += "    💎 ◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆ 💎\n"
        contrib_content += "         🎊 ¡ÚNETE A LA AVENTURA! 🎊\n"
        contrib_content += "           💝 ¡SOIS INCREÍBLES! 💝\n"

        file_path = os.path.join(OBS_FILES_DIR, TOP_CONTRIBUTORS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(contrib_content)
        except Exception as e:
            print(f"Error creando archivo inicial de contribuidores: {e}")

        # Archivo de estadísticas inicial súper decorado
        stats_content = "╔══════════════════════════════════════════════════════╗\n"
        stats_content += "║              📊 ESTADÍSTICAS LIVE 📊                ║\n"
        stats_content += "║                🚀 STREAM STATUS 🚀                  ║\n"
        stats_content += "╚══════════════════════════════════════════════════════╝\n\n"
        stats_content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n\n"
        stats_content += "    ⏰ ▓▓▓ TIEMPO EN VIVO ▓▓▓\n"
        stats_content += "       🔥 INICIANDO STREAM 🔥\n"
        stats_content += "       🌟 ¡EMPEZANDO AVENTURA! 🌟\n"
        stats_content += "    ═══════════════════════════════════════════════\n\n"
        stats_content += "    👥 ▓▓▓ COMUNIDAD ACTIVA ▓▓▓\n"
        stats_content += "       💫 ESPERANDO ESPECTADORES 💫\n"
        stats_content += "       📊 [░░░░░░░░░░░░░░░░░░░░] 0\n"
        stats_content += "       💫 ¡ÚNETE AL CHAT! 💫\n"
        stats_content += "    ═══════════════════════════════════════════════\n\n"
        stats_content += "    💬 ▓▓▓ ACTIVIDAD DEL CHAT ▓▓▓\n"
        stats_content += "       ✨ ESPERANDO MENSAJES ✨\n"
        stats_content += "       📈 [░░░░░░░░░░░░░░░░░░░░] 0msg\n"
        stats_content += "    ═══════════════════════════════════════════════\n\n"
        stats_content += "    💰 ▓▓▓ ECONOMÍA DEL STREAM ▓▓▓\n"
        stats_content += "       💎 PREPARANDO PUNTOS 💎\n"
        stats_content += "       💸 [░░░░░░░░░░░░░░░░░░░░] 0pts\n"
        stats_content += "    ═══════════════════════════════════════════════\n\n"
        stats_content += "    🎮 ▓▓▓ COLA DE NIVELES ▓▓▓\n"
        stats_content += "       🎯 ESPERANDO NIVELES 🎯\n"
        stats_content += "       📋 [░░░░░░░░░░░░░░░░░░░░] 0lvl\n"
        stats_content += "       💫 ¡ENVÍA TU NIVEL! 💫\n"
        stats_content += "    ═══════════════════════════════════════════════\n\n"
        stats_content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n"
        stats_content += "         🎊 ¡EL STREAM ESTÁ COMENZANDO! 🎊\n"
        stats_content += "           💖 ¡BIENVENIDOS A TODOS! 💖\n"
        stats_content += "             🚀 ¡VAMOS A JUGAR! 🚀\n"

        file_path = os.path.join(OBS_FILES_DIR, STREAM_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(stats_content)
        except Exception as e:
            print(f"Error creando archivo inicial de estadísticas: {e}")

        # Archivo de nivel actual inicial
        current_level_content = "╔══════════════════════════════════════════════════════╗\n"
        current_level_content += "║               🎯 NIVEL ACTUAL 🎯                    ║\n"
        current_level_content += "║                🔥 NOW PLAYING 🔥                    ║\n"
        current_level_content += "╚══════════════════════════════════════════════════════╝\n\n"
        current_level_content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n\n"
        current_level_content += "              💫 NINGÚN NIVEL ACTIVO 💫\n\n"
        current_level_content += "    ▓▓ 🎮 ¡ESPERANDO PRÓXIMO NIVEL! 🎮 ▓▓\n"
        current_level_content += "    ▒▒ ⚡ ¡USA !lvl PARA MOSTRAR! ⚡ ▒▒\n"
        current_level_content += "    ░░ 🌟 ¡ENVÍA NIVELES CON !add! 🌟 ░░\n\n"
        current_level_content += "    ⭐ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ⭐\n"
        current_level_content += "         🎊 ¡BUENA SUERTE EN EL NIVEL! 🎊\n"
        current_level_content += "           💖 ¡DISFRUTA JUGANDO! 💖\n"

        file_path = os.path.join(OBS_FILES_DIR, CURRENT_LEVEL_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(current_level_content)
        except Exception as e:
            print(f"Error creando archivo inicial de nivel actual: {e}")

        # Archivo de próximos niveles inicial
        next_levels_content = "╔══════════════════════════════════════════════════════╗\n"
        next_levels_content += "║              🎮 PRÓXIMOS NIVELES 🎮                 ║\n"
        next_levels_content += "║                 ⚡ QUEUE LIST ⚡                    ║\n"
        next_levels_content += "╚══════════════════════════════════════════════════════╝\n\n"
        next_levels_content += "    🌟 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 🌟\n\n"
        next_levels_content += "              💫 COLA VACÍA 💫\n\n"
        next_levels_content += "    ▓▓ 🎮 ¡ENVÍA TU NIVEL CON !add <ID>! 🎮 ▓▓\n"
        next_levels_content += "    ▒▒ ⭐ ¡SÉ EL PRIMERO EN LA COLA! ⭐ ▒▒\n"
        next_levels_content += "    ░░ 🏆 ¡GANA PUNTOS POR ENVIAR! 🏆 ░░\n\n"
        next_levels_content += "         🎊 ¡ESPERANDO TU NIVEL! 🎊\n\n"
        next_levels_content += "    🌟 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 🌟\n"
        next_levels_content += "         🎉 ¡GRACIAS POR ENVIAR NIVELES! 🎉\n"
        next_levels_content += "           💖 ¡COMUNIDAD INCREÍBLE! 💖\n"

        file_path = os.path.join(OBS_FILES_DIR, NEXT_LEVELS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(next_levels_content)
        except Exception as e:
            print(f"Error creando archivo inicial de próximos niveles: {e}")

        # Archivos compactos iniciales
        compact_viewers_content = "🔥 TOP ACTIVOS 🔥\n"
        compact_viewers_content += "═══════════════════\n"
        compact_viewers_content += "Esperando actividad...\n"
        compact_viewers_content += "¡Escribe en el chat!\n"
        compact_viewers_content += "═══════════════════\n"
        compact_viewers_content += "💖 ¡Únete al chat!"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(compact_viewers_content)
        except Exception as e:
            print(f"Error creando archivo compacto inicial de viewers: {e}")

        compact_stats_content = "📊 STATS LIVE 📊\n"
        compact_stats_content += "═══════════════════\n"
        compact_stats_content += "⏰ Tiempo: Iniciando\n"
        compact_stats_content += "👥 Activos: 0\n"
        compact_stats_content += "💬 Mensajes: 0\n"
        compact_stats_content += "🎮 Cola: 0 niveles\n"
        compact_stats_content += "═══════════════════\n"
        compact_stats_content += "🚀 ¡Stream comenzando!"

        file_path = os.path.join(OBS_FILES_DIR, COMPACT_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(compact_stats_content)
        except Exception as e:
            print(f"Error creando archivo compacto inicial de stats: {e}")

        # Archivos mini (ultra compactos) iniciales
        mini_viewers_content = "🔥 TOP 3 🔥\n"
        mini_viewers_content += "Esperando...\n"
        mini_viewers_content += "¡Escribe!"

        file_path = os.path.join(OBS_FILES_DIR, MINI_VIEWERS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(mini_viewers_content)
        except Exception as e:
            print(f"Error creando archivo mini inicial de viewers: {e}")

        mini_stats_content = "📊 LIVE\n"
        mini_stats_content += "⏰ 0h0m\n"
        mini_stats_content += "👥 0\n"
        mini_stats_content += "🎮 0"

        file_path = os.path.join(OBS_FILES_DIR, MINI_STATS_FILE)
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(mini_stats_content)
        except Exception as e:
            print(f"Error creando archivo mini inicial de stats: {e}")

    async def event_message(self, message):
        # Rastrear usuarios activos
        if message.author:
            username = message.author.name.lower()
            channel = message.channel.name.lower()

            self.active_chatters.add(username)

            # Actualizar actividad para archivos OBS
            self.update_user_activity(channel, username)

            # Generar archivos OBS cada 10 mensajes para no sobrecargar
            if hasattr(self, '_message_count'):
                self._message_count += 1
            else:
                self._message_count = 1

            if self._message_count % 10 == 0:
                self.generate_obs_files(channel)

        # Verificar si el mensaje debe ser destacado
        if message.author and message.content and not message.content.startswith('!'):
            await self.check_highlighted_message(message)

        # Traducción automática si está habilitada
        if message.author and message.content and not message.content.startswith('!'):
            await self.auto_translate_message(message)

        # No convertir el contenido del mensaje a minúsculas para preservar mayúsculas/minúsculas en argumentos
        await self.handle_commands(message)

    async def check_highlighted_message(self, message):
        """Verifica si el mensaje debe ser destacado por Channel Points"""
        channel = message.channel.name.lower()
        username = message.author.name.lower()

        if channel in user_data and username in user_data[channel]:
            if user_data[channel][username].get("highlight_next", False):
                # Destacar el mensaje
                highlighted_msg = f"✨🌟 MENSAJE DESTACADO 🌟✨ @{message.author.name}: {message.content} ✨🌟✨"
                await message.channel.send(highlighted_msg)

                # Quitar el flag de destacar
                user_data[channel][username]["highlight_next"] = False

    async def auto_translate_message(self, message):
        """Detecta y traduce automáticamente mensajes en otros idiomas (mejorado)"""
        channel = message.channel.name.lower()

        # Verificar si la traducción automática está habilitada para este canal
        if channel not in translation_config or not translation_config[channel]["auto_translate"]:
            return

        text = message.content.strip()
        config = translation_config[channel]

        # Reducir longitud mínima para ser más sensible (de 10 a 5)
        min_length = max(5, config.get("min_length", 5))
        if len(text) < min_length:
            return

        # Ignorar mensajes que son solo emojis o números
        import re
        if re.match(r'^[\d\s\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+$', text):
            return

        # Detectar idioma
        detected_lang = self.detect_language(text)

        # Si no se detecta idioma o ya está en español, no traducir
        if not detected_lang or detected_lang == "es":
            return

        # Intentar traducir
        target_lang = config["target_language"]
        translated = self.translate_text(text, detected_lang, target_lang)

        if translated and translated.lower() != text.lower():
            # Mapeo de códigos de idioma a nombres con banderas
            lang_names = {
                "en": "🇺🇸 Inglés",
                "fr": "🇫🇷 Francés",
                "de": "🇩🇪 Alemán",
                "it": "🇮🇹 Italiano",
                "pt": "🇧🇷 Portugués",
                "ru": "🇷🇺 Ruso"
            }

            lang_name = lang_names.get(detected_lang, f"🌐 {detected_lang.upper()}")
            target_name = lang_names.get(target_lang, f"🌐 {target_lang.upper()}")

            # Enviar traducción con formato mejorado
            translation_msg = f"🌐 [{lang_name} → {target_name}] @{message.author.name}: {translated}"
            await message.channel.send(translation_msg)

            # Opcional: Log para debugging
            print(f"[AUTO-TRANSLATE] {channel} | {detected_lang} → {target_lang} | '{text}' → '{translated}'")

    @commands.command(name='lvlA')
    async def random_level(self, ctx):
        """Muestra un nivel aleatorio de la lista y lo remueve"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()
        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("No hay niveles en la lista.")
            return

        # Seleccionar nivel aleatorio y removerlo de la lista
        random_index = random.randint(0, len(lista_ids_per_channel[channel]) - 1)
        level_id, submitter_user = lista_ids_per_channel[channel].pop(random_index)

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://gdbrowser.com/api/level/{level_id}") as resp:
                    if resp.status != 200:
                        await ctx.send(f"No se pudo obtener información del nivel {level_id}.")
                        return
                    data = await resp.json()
        except Exception as e:
            await ctx.send(f"Error al obtener información del nivel {level_id}: {e}")
            return

        # Extraer información del nivel
        nombre = data.get("name", "Desconocido")
        autor = data.get("author", "Desconocido")
        estrellas = data.get("stars", "?")
        dificultad = data.get("difficulty", "?")
        downloads = data.get("downloads", 0)
        likes = data.get("likes", 0)

        # Guardar datos del último nivel mostrado
        self.last_level_data = {
            "id": level_id,
            "name": nombre,
            "author": autor,
            "stars": estrellas,
            "difficulty": dificultad,
            "downloads": downloads,
            "likes": likes
        }

        # Generar archivo OBS del nivel actual
        self.generate_current_level_file(self.last_level_data)

        # Actualizar archivo de próximos niveles
        self.generate_next_levels_file(channel)

        # El indicador Send se basa en si el nivel existe (status 200)
        send_indicator = "Send🟢"

        await ctx.send(f"ID: {level_id} | Nivel: {nombre} | Autor: {autor} | ⭐ {estrellas} | Dificultad: {dificultad} | 👍 {likes} | ⬇️ {downloads} | {send_indicator} | Nivel enviado por: @{submitter_user}")

    @commands.command(name='sorteo')
    async def iniciar_sorteo(self, ctx):
        """Inicia un sorteo en el canal"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        # Verificar si ya hay un sorteo activo
        if channel in sorteos_activos and sorteos_activos[channel]["activo"]:
            await ctx.send(f"@{ctx.author.name} ya hay un sorteo activo en este canal. Usa !cancelsorteo para cancelarlo primero.")
            return

        parts = ctx.message.content.split(maxsplit=2)
        if len(parts) < 2:
            await ctx.send("Uso correcto: !sorteo <premio> [duración_en_minutos]")
            return

        premio = parts[1]
        duracion = 5  # Duración por defecto en minutos

        # Si se especifica duración
        if len(parts) > 2:
            try:
                duracion = int(parts[2])
                if duracion < 1 or duracion > 60:
                    await ctx.send(f"@{ctx.author.name} la duración debe estar entre 1 y 60 minutos.")
                    return
            except ValueError:
                await ctx.send(f"@{ctx.author.name} la duración debe ser un número válido.")
                return

        # Inicializar sorteo
        sorteos_activos[channel] = {
            "premio": premio,
            "participantes": set(),
            "activo": True,
            "inicio": datetime.datetime.now(),
            "duracion": duracion,
            "iniciado_por": ctx.author.name
        }

        await ctx.send(f"🎉 ¡SORTEO INICIADO! 🎉 Premio: {premio} | Duración: {duracion} minutos | Escribe !participar para unirte | Iniciado por: @{ctx.author.name}")

    @commands.command(name='participar')
    async def participar_sorteo(self, ctx):
        """Permite a un usuario participar en el sorteo activo"""
        channel = ctx.channel.name.lower()
        user = ctx.author.name.lower()

        # Verificar si hay un sorteo activo
        if channel not in sorteos_activos or not sorteos_activos[channel]["activo"]:
            await ctx.send(f"@{ctx.author.name} no hay ningún sorteo activo en este momento.")
            return

        # Verificar si el sorteo ha expirado
        sorteo = sorteos_activos[channel]
        tiempo_transcurrido = datetime.datetime.now() - sorteo["inicio"]
        if tiempo_transcurrido.total_seconds() > sorteo["duracion"] * 60:
            sorteo["activo"] = False
            await ctx.send(f"@{ctx.author.name} el sorteo ha expirado. Usa !ganador para seleccionar un ganador.")
            return

        # Verificar si el usuario ya está participando
        if user in sorteo["participantes"]:
            await ctx.send(f"@{ctx.author.name} ya estás participando en el sorteo.")
            return

        # Agregar usuario al sorteo
        sorteo["participantes"].add(user)
        await ctx.send(f"@{ctx.author.name} te has unido al sorteo! 🎉 Total de participantes: {len(sorteo['participantes'])}")

    @commands.command(name='ganador')
    async def seleccionar_ganador(self, ctx):
        """Selecciona un ganador aleatorio del sorteo activo"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        # Verificar si hay un sorteo
        if channel not in sorteos_activos:
            await ctx.send(f"@{ctx.author.name} no hay ningún sorteo en este canal.")
            return

        sorteo = sorteos_activos[channel]

        # Verificar si hay participantes
        if not sorteo["participantes"]:
            await ctx.send(f"@{ctx.author.name} no hay participantes en el sorteo.")
            return

        # Seleccionar ganador aleatorio
        ganador = random.choice(list(sorteo["participantes"]))
        premio = sorteo["premio"]
        total_participantes = len(sorteo["participantes"])

        # Finalizar sorteo
        sorteo["activo"] = False

        await ctx.send(f"🏆 ¡FELICIDADES @{ganador}! 🏆 Has ganado: {premio} | Total de participantes: {total_participantes} | ¡Gracias a todos por participar! 🎉")

    @commands.command(name='sorteoinfo')
    async def info_sorteo(self, ctx):
        """Muestra información del sorteo activo"""
        channel = ctx.channel.name.lower()

        # Verificar si hay un sorteo
        if channel not in sorteos_activos:
            await ctx.send(f"@{ctx.author.name} no hay ningún sorteo en este canal.")
            return

        sorteo = sorteos_activos[channel]

        if not sorteo["activo"]:
            await ctx.send(f"@{ctx.author.name} el último sorteo ya ha finalizado.")
            return

        # Calcular tiempo restante
        tiempo_transcurrido = datetime.datetime.now() - sorteo["inicio"]
        tiempo_restante = sorteo["duracion"] * 60 - tiempo_transcurrido.total_seconds()

        if tiempo_restante <= 0:
            sorteo["activo"] = False
            await ctx.send(f"@{ctx.author.name} el sorteo ha expirado. Usa !ganador para seleccionar un ganador.")
            return

        minutos_restantes = int(tiempo_restante // 60)
        segundos_restantes = int(tiempo_restante % 60)

        await ctx.send(f"🎉 SORTEO ACTIVO 🎉 Premio: {sorteo['premio']} | Participantes: {len(sorteo['participantes'])} | Tiempo restante: {minutos_restantes}m {segundos_restantes}s | Iniciado por: @{sorteo['iniciado_por']} | Escribe !participar para unirte")

    @commands.command(name='cancelsorteo')
    async def cancelar_sorteo(self, ctx):
        """Cancela el sorteo activo"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        # Verificar si hay un sorteo activo
        if channel not in sorteos_activos or not sorteos_activos[channel]["activo"]:
            await ctx.send(f"@{ctx.author.name} no hay ningún sorteo activo para cancelar.")
            return

        sorteo = sorteos_activos[channel]
        premio = sorteo["premio"]
        participantes = len(sorteo["participantes"])

        # Cancelar sorteo
        sorteo["activo"] = False

        await ctx.send(f"❌ SORTEO CANCELADO ❌ El sorteo por '{premio}' ha sido cancelado por @{ctx.author.name} | Había {participantes} participantes")

    # ==================== GESTIÓN AVANZADA DE NIVELES ====================

    @commands.command(name='queue')
    async def mostrar_cola(self, ctx):
        """Muestra la cola completa de niveles"""
        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("La cola de niveles está vacía.")
            return

        cola = lista_ids_per_channel[channel]
        if len(cola) <= 5:
            # Mostrar toda la cola si son 5 o menos
            queue_text = "📋 COLA DE NIVELES:\n"
            for i, (level_id, user) in enumerate(cola, 1):
                queue_text += f"{i}. ID: {level_id} (por @{user})\n"
        else:
            # Mostrar solo los primeros 5
            queue_text = f"📋 COLA DE NIVELES (mostrando 5 de {len(cola)}):\n"
            for i, (level_id, user) in enumerate(cola[:5], 1):
                queue_text += f"{i}. ID: {level_id} (por @{user})\n"
            queue_text += f"... y {len(cola) - 5} más"

        await ctx.send(queue_text)

    @commands.command(name='remove')
    async def remover_nivel(self, ctx):
        """Remueve un nivel específico de la cola por posición"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !remove <posición>")
            return

        try:
            position = int(parts[1]) - 1  # Convertir a índice (base 0)
        except ValueError:
            await ctx.send(f"@{ctx.author.name} la posición debe ser un número válido.")
            return

        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("La cola de niveles está vacía.")
            return

        cola = lista_ids_per_channel[channel]

        if position < 0 or position >= len(cola):
            await ctx.send(f"@{ctx.author.name} posición inválida. La cola tiene {len(cola)} niveles.")
            return

        level_id, user = cola.pop(position)
        await ctx.send(f"@{ctx.author.name} removido nivel {level_id} (enviado por @{user}) de la posición {position + 1}.")

    @commands.command(name='clear')
    async def limpiar_cola(self, ctx):
        """Limpia toda la cola de niveles"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("La cola de niveles ya está vacía.")
            return

        count = len(lista_ids_per_channel[channel])
        lista_ids_per_channel[channel].clear()
        await ctx.send(f"@{ctx.author.name} se han eliminado {count} niveles de la cola. La cola está ahora vacía.")

    @commands.command(name='skip')
    async def saltar_nivel(self, ctx):
        """Salta el nivel actual sin jugarlo"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel or not lista_ids_per_channel[channel]:
            await ctx.send("No hay niveles en la cola para saltar.")
            return

        level_id, user = lista_ids_per_channel[channel].pop(0)
        await ctx.send(f"@{ctx.author.name} saltó el nivel {level_id} (enviado por @{user}). Niveles restantes: {len(lista_ids_per_channel[channel])}")

    @commands.command(name='stats')
    async def estadisticas_usuario(self, ctx):
        """Muestra estadísticas de niveles enviados por un usuario"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            target_user = ctx.author.name.lower()
        else:
            target_user = parts[1].lower()

        channel = ctx.channel.name.lower()

        if channel not in lista_ids_per_channel:
            await ctx.send(f"@{ctx.author.name} no hay estadísticas disponibles para este canal.")
            return

        # Contar niveles del usuario en la cola actual
        current_levels = sum(1 for (_, user) in lista_ids_per_channel[channel] if user == target_user)

        # Inicializar estadísticas si no existen
        if channel not in level_stats:
            level_stats[channel] = {"total_levels": 0, "levels_by_user": {}, "levels_by_difficulty": {}}

        # Obtener estadísticas históricas
        total_submitted = level_stats[channel]["levels_by_user"].get(target_user, 0)

        await ctx.send(f"📊 ESTADÍSTICAS DE @{target_user.upper()}: Niveles en cola: {current_levels} | Total enviados: {total_submitted}")

    @commands.command(name='top')
    async def top_usuarios(self, ctx):
        """Muestra el top 5 de usuarios que más niveles han enviado"""
        channel = ctx.channel.name.lower()

        if channel not in level_stats or not level_stats[channel]["levels_by_user"]:
            await ctx.send("No hay estadísticas disponibles aún.")
            return

        # Ordenar usuarios por niveles enviados
        sorted_users = sorted(level_stats[channel]["levels_by_user"].items(), key=lambda x: x[1], reverse=True)

        top_text = "🏆 TOP 5 USUARIOS (niveles enviados):\n"
        for i, (user, count) in enumerate(sorted_users[:5], 1):
            medal = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i-1]
            top_text += f"{medal} @{user}: {count} niveles\n"

        await ctx.send(top_text)

    # ==================== SISTEMA DE PUNTOS Y RECOMPENSAS ====================

    def init_user_data(self, channel, user):
        """Inicializa datos del usuario si no existen"""
        if channel not in user_data:
            user_data[channel] = {}
        if user not in user_data[channel]:
            user_data[channel][user] = {
                "points": 0,
                "last_daily": None,
                "levels_submitted": 0,
                "achievements": set(),
                "last_activity": datetime.datetime.now()
            }

    @commands.command(name='points')
    async def mostrar_puntos(self, ctx):
        """Muestra los puntos actuales del usuario"""
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        self.init_user_data(channel, user)
        points = user_data[channel][user]["points"]

        await ctx.send(f"@{ctx.author.name} tienes {points} puntos 💰")

    @commands.command(name='daily')
    async def puntos_diarios(self, ctx):
        """Reclama puntos diarios"""
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        self.init_user_data(channel, user)

        now = datetime.datetime.now()
        last_daily = user_data[channel][user]["last_daily"]

        # Verificar si ya reclamó hoy
        if last_daily and (now - last_daily).days < 1:
            hours_left = 24 - (now - last_daily).seconds // 3600
            await ctx.send(f"@{ctx.author.name} ya reclamaste tus puntos diarios. Vuelve en {hours_left} horas.")
            return

        # Dar puntos diarios (50-100 aleatorio)
        daily_points = random.randint(50, 100)
        user_data[channel][user]["points"] += daily_points
        user_data[channel][user]["last_daily"] = now

        await ctx.send(f"@{ctx.author.name} has reclamado {daily_points} puntos diarios! 💰 Total: {user_data[channel][user]['points']} puntos")

    @commands.command(name='give')
    async def dar_puntos(self, ctx):
        """Da puntos a otro usuario (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 3:
            await ctx.send("Uso correcto: !give <usuario> <puntos>")
            return

        target_user = parts[1].lower()
        try:
            points = int(parts[2])
            if points <= 0:
                await ctx.send(f"@{ctx.author.name} la cantidad debe ser positiva.")
                return
        except ValueError:
            await ctx.send(f"@{ctx.author.name} la cantidad debe ser un número válido.")
            return

        channel = ctx.channel.name.lower()
        self.init_user_data(channel, target_user)

        user_data[channel][target_user]["points"] += points
        await ctx.send(f"@{ctx.author.name} ha dado {points} puntos a @{target_user}. Nuevo total: {user_data[channel][target_user]['points']} puntos 💰")

    @commands.command(name='shop')
    async def mostrar_tienda(self, ctx):
        """Muestra la tienda de recompensas"""
        shop_items = (
            "🛒 TIENDA DE PUNTOS:\n"
            "1. Destacar mensaje (100 puntos) - !buy highlight\n"
            "2. Elegir próximo nivel (200 puntos) - !buy chooselevel\n"
            "3. Saltarse la cola (300 puntos) - !buy skipqueue\n"
            "4. Mensaje personalizado (150 puntos) - !buy message\n"
            "5. Emote personalizado (250 puntos) - !buy emote\n"
            "Usa !buy <item> para comprar"
        )
        await ctx.send(shop_items)

    @commands.command(name='buy')
    async def comprar_item(self, ctx):
        """Compra items de la tienda"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso correcto: !buy <item>. Usa !shop para ver items disponibles.")
            return

        item = parts[1].lower()
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        self.init_user_data(channel, user)
        user_points = user_data[channel][user]["points"]

        # Definir precios y efectos
        shop_prices = {
            "highlight": 100,
            "chooselevel": 200,
            "skipqueue": 300,
            "message": 150,
            "emote": 250
        }

        if item not in shop_prices:
            await ctx.send(f"@{ctx.author.name} item no válido. Usa !shop para ver items disponibles.")
            return

        price = shop_prices[item]

        if user_points < price:
            await ctx.send(f"@{ctx.author.name} no tienes suficientes puntos. Necesitas {price} puntos, tienes {user_points}.")
            return

        # Procesar compra
        user_data[channel][user]["points"] -= price

        if item == "highlight":
            await ctx.send(f"✨ @{ctx.author.name} ha comprado DESTACAR MENSAJE! Su próximo mensaje será destacado. Puntos restantes: {user_data[channel][user]['points']}")
        elif item == "chooselevel":
            await ctx.send(f"🎯 @{ctx.author.name} ha comprado ELEGIR PRÓXIMO NIVEL! Puede elegir el próximo nivel a jugar. Puntos restantes: {user_data[channel][user]['points']}")
        elif item == "skipqueue":
            await ctx.send(f"⏭️ @{ctx.author.name} ha comprado SALTARSE LA COLA! Su próximo nivel irá al frente. Puntos restantes: {user_data[channel][user]['points']}")
        elif item == "message":
            await ctx.send(f"💬 @{ctx.author.name} ha comprado MENSAJE PERSONALIZADO! Puede enviar un mensaje especial. Puntos restantes: {user_data[channel][user]['points']}")
        elif item == "emote":
            await ctx.send(f"😎 @{ctx.author.name} ha comprado EMOTE PERSONALIZADO! Puede usar un emote especial. Puntos restantes: {user_data[channel][user]['points']}")

    # ==================== JUEGOS DE CHAT ====================

    @commands.command(name='8ball')
    async def eight_ball(self, ctx):
        """Bola mágica 8 con respuestas aleatorias"""
        parts = ctx.message.content.split(maxsplit=1)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} debes hacer una pregunta. Ejemplo: !8ball ¿voy a ganar?")
            return

        responses = [
            "Sí, definitivamente", "Es cierto", "Sin duda", "Sí", "Puedes confiar en ello",
            "Como yo lo veo, sí", "Muy probable", "Las perspectivas son buenas", "Sí, y los signos apuntan a que sí",
            "Respuesta confusa, intenta de nuevo", "Pregunta de nuevo más tarde", "Mejor no te lo digo ahora",
            "No puedo predecir ahora", "Concéntrate y pregunta de nuevo",
            "No cuentes con ello", "Mi respuesta es no", "Mis fuentes dicen que no",
            "Las perspectivas no son tan buenas", "Muy dudoso"
        ]

        response = random.choice(responses)
        await ctx.send(f"🎱 @{ctx.author.name} {response}")

    @commands.command(name='roll')
    async def roll_dice(self, ctx):
        """Tirar dados"""
        parts = ctx.message.content.split()
        max_num = 100  # Por defecto

        if len(parts) > 1:
            try:
                max_num = int(parts[1])
                if max_num < 1 or max_num > 1000:
                    await ctx.send(f"@{ctx.author.name} el número debe estar entre 1 y 1000.")
                    return
            except ValueError:
                await ctx.send(f"@{ctx.author.name} debes especificar un número válido.")
                return

        result = random.randint(1, max_num)
        await ctx.send(f"🎲 @{ctx.author.name} rodó {result} (1-{max_num})")

    @commands.command(name='flip')
    async def flip_coin(self, ctx):
        """Lanzar moneda"""
        result = random.choice(["Cara", "Cruz"])
        emoji = "🪙" if result == "Cara" else "🔄"
        await ctx.send(f"{emoji} @{ctx.author.name} {result}!")

    @commands.command(name='rps')
    async def rock_paper_scissors(self, ctx):
        """Piedra, papel o tijera contra el bot"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} elige: !rps <piedra/papel/tijera>")
            return

        user_choice = parts[1].lower()
        valid_choices = ["piedra", "papel", "tijera", "rock", "paper", "scissors"]

        if user_choice not in valid_choices:
            await ctx.send(f"@{ctx.author.name} opción inválida. Usa: piedra, papel o tijera")
            return

        # Normalizar entrada
        choice_map = {"rock": "piedra", "paper": "papel", "scissors": "tijera"}
        user_choice = choice_map.get(user_choice, user_choice)

        bot_choice = random.choice(["piedra", "papel", "tijera"])

        # Determinar ganador
        if user_choice == bot_choice:
            result = "¡Empate!"
        elif (user_choice == "piedra" and bot_choice == "tijera") or \
             (user_choice == "papel" and bot_choice == "piedra") or \
             (user_choice == "tijera" and bot_choice == "papel"):
            result = "¡Ganaste!"
        else:
            result = "¡Perdiste!"

        emojis = {"piedra": "🪨", "papel": "📄", "tijera": "✂️"}
        await ctx.send(f"{emojis[user_choice]} vs {emojis[bot_choice]} | @{ctx.author.name} {result}")

    @commands.command(name='trivia')
    async def trivia_gd(self, ctx):
        """Preguntas de trivia sobre Geometry Dash"""
        questions = [
            {"q": "¿Quién es el creador de Geometry Dash?", "a": "RobTop"},
            {"q": "¿Cuál es el nivel más difícil oficial?", "a": "Deadlocked"},
            {"q": "¿En qué año se lanzó Geometry Dash?", "a": "2013"},
            {"q": "¿Cuál es el primer nivel demon oficial?", "a": "The Nightmare"},
            {"q": "¿Cuántos niveles oficiales hay en total?", "a": "21"},
            {"q": "¿Cómo se llama el cubo principal?", "a": "Geometry Dash Cube"},
            {"q": "¿Cuál es el nivel más largo oficial?", "a": "Deadlocked"},
            {"q": "¿Qué significa GD?", "a": "Geometry Dash"}
        ]

        question_data = random.choice(questions)
        await ctx.send(f"🧠 TRIVIA GD: {question_data['q']} (Responde en chat)")
        # Nota: En una implementación completa, aquí guardarías la respuesta y verificarías las respuestas de los usuarios

    @commands.command(name='quote')
    async def random_quote(self, ctx):
        """Frases inspiradoras aleatorias"""
        quotes = [
            "El éxito es la suma de pequeños esfuerzos repetidos día tras día.",
            "No te rindas, cada experto fue una vez un principiante.",
            "La práctica hace al maestro.",
            "Los sueños no tienen fecha de caducidad.",
            "El único modo de hacer un gran trabajo es amar lo que haces.",
            "No cuentes los días, haz que los días cuenten.",
            "La diferencia entre lo ordinario y lo extraordinario es esa pequeña 'extra'.",
            "El fracaso es simplemente la oportunidad de comenzar de nuevo de forma más inteligente.",
            "Cree en ti mismo y todo será posible.",
            "La motivación te pone en marcha, el hábito te mantiene en movimiento."
        ]

        quote = random.choice(quotes)
        await ctx.send(f"💭 \"{quote}\"")

    # ==================== COMANDOS DIVERTIDOS ====================

    @commands.command(name='hug')
    async def hug_user(self, ctx):
        """Abrazar a otro usuario"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} ¿a quién quieres abrazar? Uso: !hug <usuario>")
            return

        target = parts[1].replace('@', '')
        hugs = [
            f"🤗 @{ctx.author.name} le da un abrazo cálido a @{target}!",
            f"🫂 @{ctx.author.name} abraza fuertemente a @{target}!",
            f"💕 @{ctx.author.name} envuelve a @{target} en un abrazo de oso!",
            f"🤗 ¡Abrazo grupal! @{ctx.author.name} y @{target} se abrazan!"
        ]

        await ctx.send(random.choice(hugs))

    @commands.command(name='slap')
    async def slap_user(self, ctx):
        """Golpear juguetonamente a otro usuario"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} ¿a quién quieres golpear? Uso: !slap <usuario>")
            return

        target = parts[1].replace('@', '')
        slaps = [
            f"👋 @{ctx.author.name} le da una palmadita juguetona a @{target}!",
            f"🖐️ @{ctx.author.name} golpea suavemente a @{target} con un pez!",
            f"✋ @{ctx.author.name} le da un toque amistoso a @{target}!",
            f"👏 @{ctx.author.name} aplaude en la cara de @{target} juguetonamente!"
        ]

        await ctx.send(random.choice(slaps))

    @commands.command(name='love')
    async def love_user(self, ctx):
        """Mostrar amor hacia otro usuario"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} ¿a quién amas? Uso: !love <usuario>")
            return

        target = parts[1].replace('@', '')
        loves = [
            f"💖 @{ctx.author.name} ama mucho a @{target}!",
            f"❤️ @{ctx.author.name} envía amor infinito a @{target}!",
            f"💕 @{ctx.author.name} y @{target} - ¡amor verdadero!",
            f"💝 @{ctx.author.name} le regala todo su cariño a @{target}!"
        ]

        await ctx.send(random.choice(loves))

    @commands.command(name='ship')
    async def ship_users(self, ctx):
        """Calcular compatibilidad entre usuarios"""
        parts = ctx.message.content.split()
        if len(parts) < 3:
            await ctx.send(f"@{ctx.author.name} necesitas dos usuarios. Uso: !ship <usuario1> <usuario2>")
            return

        user1 = parts[1].replace('@', '')
        user2 = parts[2].replace('@', '')

        # Generar porcentaje "aleatorio" pero consistente basado en los nombres
        compatibility = abs(hash(user1 + user2)) % 101

        hearts = "💖" * (compatibility // 20)

        if compatibility >= 90:
            message = "¡Pareja perfecta!"
        elif compatibility >= 70:
            message = "¡Muy compatible!"
        elif compatibility >= 50:
            message = "Buena compatibilidad"
        elif compatibility >= 30:
            message = "Podría funcionar..."
        else:
            message = "Mejor como amigos 😅"

        await ctx.send(f"💕 SHIP: @{user1} + @{user2} = {compatibility}% {hearts} {message}")

    @commands.command(name='fact')
    async def random_fact(self, ctx):
        """Datos curiosos aleatorios"""
        facts = [
            "Los pulpos tienen tres corazones y sangre azul.",
            "Una cucaracha puede vivir hasta una semana sin cabeza.",
            "Los delfines tienen nombres para identificarse entre ellos.",
            "El corazón de un camarón está en su cabeza.",
            "Las abejas pueden reconocer rostros humanos.",
            "Los pingüinos pueden saltar hasta 2 metros de altura.",
            "El cerebro humano usa aproximadamente 20% de la energía del cuerpo.",
            "Los gatos no pueden saborear lo dulce.",
            "Un rayo es cinco veces más caliente que la superficie del sol.",
            "Los koalas duermen hasta 22 horas al día.",
            "Geometry Dash tiene más de 100 millones de descargas.",
            "RobTop tardó 4 años en hacer Geometry Dash 2.2.",
            "El nivel más jugado de GD es 'Stereo Madness'."
        ]

        fact = random.choice(facts)
        await ctx.send(f"🤓 DATO CURIOSO: {fact}")

    # ==================== ESTADÍSTICAS DEL STREAM ====================

    @commands.command(name='uptime')
    async def stream_uptime(self, ctx):
        """Tiempo que lleva el bot activo"""
        uptime = datetime.datetime.now() - self.start_time
        hours = uptime.seconds // 3600
        minutes = (uptime.seconds % 3600) // 60

        if uptime.days > 0:
            await ctx.send(f"⏰ Bot activo por: {uptime.days} días, {hours} horas y {minutes} minutos")
        else:
            await ctx.send(f"⏰ Bot activo por: {hours} horas y {minutes} minutos")

    @commands.command(name='followage')
    async def follow_age(self, ctx):
        """Tiempo que lleva siguiendo el canal (simulado)"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            target_user = ctx.author.name
        else:
            target_user = parts[1].replace('@', '')

        # Simular followage (en una implementación real usarías la API de Twitch)
        days = random.randint(1, 1000)
        months = days // 30
        remaining_days = days % 30

        if months > 0:
            await ctx.send(f"📅 @{target_user} ha estado siguiendo por {months} meses y {remaining_days} días")
        else:
            await ctx.send(f"📅 @{target_user} ha estado siguiendo por {days} días")

    @commands.command(name='viewers')
    async def current_viewers(self, ctx):
        """Número actual de viewers (simulado)"""
        # En una implementación real, usarías la API de Twitch
        viewers = random.randint(50, 500)
        await ctx.send(f"👥 Viewers actuales: {viewers}")

    @commands.command(name='chatters')
    async def active_chatters(self, ctx):
        """Lista de usuarios activos en chat"""
        if not self.active_chatters:
            await ctx.send("No hay chatters activos registrados.")
            return

        chatters_list = list(self.active_chatters)[:10]  # Mostrar solo los primeros 10
        chatters_text = ", ".join(f"@{chatter}" for chatter in chatters_list)

        if len(self.active_chatters) > 10:
            await ctx.send(f"💬 Chatters activos (mostrando 10 de {len(self.active_chatters)}): {chatters_text}")
        else:
            await ctx.send(f"💬 Chatters activos ({len(self.active_chatters)}): {chatters_text}")

    # ==================== INFORMACIÓN DEL CANAL ====================

    @commands.command(name='socials')
    async def social_media(self, ctx):
        """Redes sociales del streamer"""
        socials_info = (
            "🌐 REDES SOCIALES:\n"
            "📺 Twitch: twitch.tv/flozwer\n"
            "🎥 YouTube: [Agregar tu canal]\n"
            "💬 Discord: [Agregar tu servidor]\n"
            "🐦 Twitter: [Agregar tu Twitter]\n"
            "📸 Instagram: [Agregar tu Instagram]"
        )
        await ctx.send(socials_info)

    @commands.command(name='discord')
    async def discord_link(self, ctx):
        """Link al servidor de Discord"""
        await ctx.send("💬 ¡Únete a nuestro Discord! [Agregar link del Discord aquí]")

    @commands.command(name='youtube')
    async def youtube_link(self, ctx):
        """Link al canal de YouTube"""
        await ctx.send("🎥 ¡Suscríbete al canal de YouTube! [Agregar link del YouTube aquí]")

    @commands.command(name='donate')
    async def donation_link(self, ctx):
        """Link de donaciones"""
        await ctx.send("💝 ¡Apoya el stream! [Agregar link de donaciones aquí]")

    # ==================== UTILIDADES ====================

    @commands.command(name='time')
    async def current_time(self, ctx):
        """Hora actual"""
        parts = ctx.message.content.split()
        if len(parts) > 1:
            timezone = parts[1].upper()
            # Simulación de zonas horarias comunes
            timezones = {
                "EST": -5, "PST": -8, "GMT": 0, "CET": 1,
                "JST": 9, "AEST": 10, "MST": -7, "CST": -6
            }

            if timezone in timezones:
                offset = timezones[timezone]
                current_time = datetime.datetime.now() + datetime.timedelta(hours=offset)
                await ctx.send(f"🕐 Hora en {timezone}: {current_time.strftime('%H:%M:%S')}")
            else:
                await ctx.send(f"@{ctx.author.name} zona horaria no reconocida. Disponibles: {', '.join(timezones.keys())}")
        else:
            current_time = datetime.datetime.now()
            await ctx.send(f"🕐 Hora local: {current_time.strftime('%H:%M:%S')}")

    @commands.command(name='calc')
    async def calculator(self, ctx):
        """Calculadora simple"""
        parts = ctx.message.content.split(maxsplit=1)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !calc <operación>. Ejemplo: !calc 2+2")
            return

        expression = parts[1].replace(' ', '')

        # Solo permitir operaciones básicas seguras
        allowed_chars = set('0123456789+-*/.()%')
        if not all(c in allowed_chars for c in expression):
            await ctx.send(f"@{ctx.author.name} solo se permiten números y operadores básicos (+, -, *, /, %, paréntesis)")
            return

        try:
            result = eval(expression)
            await ctx.send(f"🧮 @{ctx.author.name} {expression} = {result}")
        except:
            await ctx.send(f"@{ctx.author.name} expresión inválida. Ejemplo: !calc 2+2*3")

    # ==================== COMANDOS PERSONALIZADOS ====================

    @commands.command(name='paimon')
    async def paimon_info(self, ctx):
        """Información sobre Paimon (el bot)"""
        paimon_info = (
            "🤖 ¡Hola! Soy Paimon, tu bot de Twitch favorito!\n"
            "✨ Puedo ayudarte con niveles de Geometry Dash, sorteos, juegos y mucho más!\n"
            "🎮 Usa !com para ver todos mis comandos\n"
            "💝 Creado con amor para la comunidad de FlozWer"
        )
        await ctx.send(paimon_info)

    @commands.command(name='genshin')
    async def genshin_info(self, ctx):
        """Información sobre Genshin Impact"""
        genshin_tips = [
            "🗡️ Recuerda hacer tus dailies en Genshin Impact!",
            "⚡ La Electro Archon es increíble para equipos de reacción!",
            "🌟 No olvides reclamar tu resina cada día!",
            "🏹 Los personajes Anemo son excelentes para exploración!",
            "💎 Ahorra tus primogemas para los banners que realmente quieres!",
            "🍖 Cocinar comida de curación es muy importante para el combate!",
            "🗺️ Explora cada región completamente para obtener todas las recompensas!"
        ]

        tip = random.choice(genshin_tips)
        await ctx.send(tip)

    @commands.command(name='geometry')
    async def geometry_tips(self, ctx):
        """Tips y trucos de Geometry Dash"""
        gd_tips = [
            "🎮 Practica en el modo práctica antes de intentar el nivel completo!",
            "🎵 Escucha la música, te ayudará con el timing!",
            "⚡ Los niveles demon requieren mucha paciencia y práctica!",
            "🔄 Si te frustras, toma un descanso y vuelve más tarde!",
            "📱 Usa el modo lento para aprender partes difíciles!",
            "🎯 Concéntrate en una sección a la vez!",
            "💪 La consistencia es más importante que la velocidad!",
            "🌟 ¡Cada muerte te acerca más a completar el nivel!"
        ]

        tip = random.choice(gd_tips)
        await ctx.send(tip)

    @commands.command(name='creator')
    async def creator_info(self, ctx):
        """Información sobre ser creator en GD"""
        creator_info = (
            "🎨 CONSEJOS PARA CREATORS DE GD:\n"
            "✨ La decoración es tan importante como la jugabilidad\n"
            "🎵 Sincroniza tu nivel con la música\n"
            "⚖️ Balancea la dificultad progresivamente\n"
            "🧪 Testea tu nivel muchas veces antes de publicarlo\n"
            "💡 Inspírate en otros creators pero mantén tu estilo único\n"
            "📝 Dale un buen nombre y descripción a tu nivel"
        )
        await ctx.send(creator_info)

    # ==================== INFORMACIÓN DE GEOMETRY DASH ====================

    @commands.command(name='search')
    async def search_level(self, ctx):
        """Buscar niveles por nombre"""
        parts = ctx.message.content.split(maxsplit=1)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !search <nombre del nivel>")
            return

        search_term = parts[1]

        try:
            async with aiohttp.ClientSession() as session:
                # Usar la API de GDBrowser para buscar
                async with session.get(f"https://gdbrowser.com/api/search/{search_term}") as resp:
                    if resp.status != 200:
                        await ctx.send(f"@{ctx.author.name} no se pudo realizar la búsqueda.")
                        return
                    data = await resp.json()

                    if not data:
                        await ctx.send(f"@{ctx.author.name} no se encontraron niveles con '{search_term}'.")
                        return

                    # Mostrar los primeros 3 resultados
                    results = data[:3]
                    search_text = f"🔍 RESULTADOS PARA '{search_term}':\n"

                    for i, level in enumerate(results, 1):
                        name = level.get('name', 'Desconocido')
                        author = level.get('author', 'Desconocido')
                        level_id = level.get('id', '?')
                        difficulty = level.get('difficulty', '?')
                        search_text += f"{i}. {name} por {author} (ID: {level_id}) - {difficulty}\n"

                    await ctx.send(search_text)
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error al buscar: {e}")

    @commands.command(name='profile')
    async def gd_profile(self, ctx):
        """Información del perfil de Geometry Dash"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !profile <usuario_gd>")
            return

        username = parts[1]

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://gdbrowser.com/api/profile/{username}") as resp:
                    if resp.status != 200:
                        await ctx.send(f"@{ctx.author.name} no se encontró el usuario '{username}'.")
                        return
                    data = await resp.json()

                    username = data.get('username', 'Desconocido')
                    stars = data.get('stars', 0)
                    demons = data.get('demons', 0)
                    cp = data.get('cp', 0)
                    coins = data.get('coins', 0)

                    profile_text = (
                        f"👤 PERFIL GD: {username}\n"
                        f"⭐ Estrellas: {stars}\n"
                        f"👹 Demons: {demons}\n"
                        f"🏆 Creator Points: {cp}\n"
                        f"🪙 Monedas: {coins}"
                    )

                    await ctx.send(profile_text)
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error al obtener perfil: {e}")

    @commands.command(name='featured')
    async def featured_levels(self, ctx):
        """Niveles destacados recientes"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://gdbrowser.com/api/featured") as resp:
                    if resp.status != 200:
                        await ctx.send("No se pudieron obtener los niveles destacados.")
                        return
                    data = await resp.json()

                    if not data:
                        await ctx.send("No hay niveles destacados disponibles.")
                        return

                    # Mostrar los primeros 3 niveles destacados
                    featured_text = "⭐ NIVELES DESTACADOS RECIENTES:\n"

                    for i, level in enumerate(data[:3], 1):
                        name = level.get('name', 'Desconocido')
                        author = level.get('author', 'Desconocido')
                        level_id = level.get('id', '?')
                        difficulty = level.get('difficulty', '?')
                        featured_text += f"{i}. {name} por {author} (ID: {level_id}) - {difficulty}\n"

                    await ctx.send(featured_text)
        except Exception as e:
            await ctx.send(f"Error al obtener niveles destacados: {e}")

    # ==================== COMANDOS ADICIONALES ====================

    @commands.command(name='weather')
    async def weather_info(self, ctx):
        """Información del clima (simulado)"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            city = "Madrid"  # Ciudad por defecto
        else:
            city = parts[1]

        # Simular datos del clima
        temperatures = list(range(15, 35))
        conditions = ["Soleado", "Nublado", "Lluvioso", "Parcialmente nublado", "Despejado"]

        temp = random.choice(temperatures)
        condition = random.choice(conditions)

        weather_emojis = {
            "Soleado": "☀️",
            "Nublado": "☁️",
            "Lluvioso": "🌧️",
            "Parcialmente nublado": "⛅",
            "Despejado": "🌤️"
        }

        emoji = weather_emojis.get(condition, "🌡️")
        await ctx.send(f"{emoji} Clima en {city}: {temp}°C, {condition}")

    @commands.command(name='translate')
    async def translate_text(self, ctx):
        """Traductor básico (simulado)"""
        parts = ctx.message.content.split(maxsplit=2)
        if len(parts) < 3:
            await ctx.send(f"@{ctx.author.name} uso: !translate <idioma> <texto>")
            return

        target_lang = parts[1].lower()
        text = parts[2]

        # Traducciones básicas simuladas
        translations = {
            "en": {"hola": "hello", "adiós": "goodbye", "gracias": "thank you", "sí": "yes", "no": "no"},
            "fr": {"hola": "bonjour", "adiós": "au revoir", "gracias": "merci", "sí": "oui", "no": "non"},
            "de": {"hola": "hallo", "adiós": "auf wiedersehen", "gracias": "danke", "sí": "ja", "no": "nein"},
            "it": {"hola": "ciao", "adiós": "arrivederci", "gracias": "grazie", "sí": "sì", "no": "no"}
        }

        if target_lang not in translations:
            await ctx.send(f"@{ctx.author.name} idiomas disponibles: en, fr, de, it")
            return

        text_lower = text.lower()
        if text_lower in translations[target_lang]:
            translated = translations[target_lang][text_lower]
            await ctx.send(f"🌐 '{text}' en {target_lang.upper()}: '{translated}'")
        else:
            await ctx.send(f"@{ctx.author.name} traducción no disponible para '{text}'")

    @commands.command(name='color')
    async def show_color(self, ctx):
        """Mostrar información de color hexadecimal"""
        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !color <código_hex>. Ejemplo: !color #FF0000")
            return

        color_code = parts[1]
        if not color_code.startswith('#'):
            color_code = '#' + color_code

        # Validar formato hexadecimal
        if len(color_code) != 7 or not all(c in '0123456789ABCDEFabcdef' for c in color_code[1:]):
            await ctx.send(f"@{ctx.author.name} formato inválido. Usa formato #RRGGBB")
            return

        # Convertir a RGB
        try:
            r = int(color_code[1:3], 16)
            g = int(color_code[3:5], 16)
            b = int(color_code[5:7], 16)

            await ctx.send(f"🎨 Color {color_code.upper()}: RGB({r}, {g}, {b})")
        except:
            await ctx.send(f"@{ctx.author.name} código de color inválido")

    @commands.command(name='schedule')
    async def stream_schedule(self, ctx):
        """Horario de streams"""
        schedule_info = (
            "📅 HORARIO DE STREAMS:\n"
            "🕐 Lunes a Viernes: 20:00 - 23:00\n"
            "🕐 Sábados: 18:00 - 00:00\n"
            "🕐 Domingos: 19:00 - 22:00\n"
            "⏰ Horario puede variar, sígueme para notificaciones!"
        )
        await ctx.send(schedule_info)

    @commands.command(name='nextstream')
    async def next_stream(self, ctx):
        """Próximo stream programado"""
        # Simular próximo stream
        days = ["Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"]
        next_day = random.choice(days)
        next_time = random.choice(["19:00", "20:00", "21:00"])

        await ctx.send(f"📺 Próximo stream: {next_day} a las {next_time} 🎮")

    @commands.command(name='remind')
    async def set_reminder(self, ctx):
        """Recordatorio personal (simulado)"""
        parts = ctx.message.content.split(maxsplit=2)
        if len(parts) < 3:
            await ctx.send(f"@{ctx.author.name} uso: !remind <tiempo_en_minutos> <mensaje>")
            return

        try:
            minutes = int(parts[1])
            if minutes < 1 or minutes > 60:
                await ctx.send(f"@{ctx.author.name} el tiempo debe estar entre 1 y 60 minutos.")
                return
        except ValueError:
            await ctx.send(f"@{ctx.author.name} el tiempo debe ser un número válido.")
            return

        message = parts[2]
        await ctx.send(f"⏰ @{ctx.author.name} recordatorio configurado para {minutes} minutos: '{message}' (Nota: Esta es una simulación)")

    @commands.command(name='achievements')
    async def show_achievements(self, ctx):
        """Mostrar logros del usuario"""
        user = ctx.author.name.lower()
        channel = ctx.channel.name.lower()

        self.init_user_data(channel, user)

        # Logros básicos simulados
        possible_achievements = [
            "🎮 Primer Nivel - Enviaste tu primer nivel",
            "🔥 Nivel Demon - Enviaste un nivel demon",
            "💰 Millonario - Alcanzaste 1000 puntos",
            "🎉 Ganador - Ganaste un sorteo",
            "📅 Fiel - 7 días consecutivos reclamando daily",
            "💬 Hablador - 100 mensajes en el chat",
            "⭐ Estrella - Nivel destacado por el streamer"
        ]

        # Simular algunos logros aleatorios
        user_achievements = random.sample(possible_achievements, random.randint(1, 4))

        achievements_text = f"🏆 LOGROS DE @{ctx.author.name.upper()}:\n"
        for achievement in user_achievements:
            achievements_text += f"{achievement}\n"

        await ctx.send(achievements_text)

    # ==================== COMANDOS DE TRADUCCIÓN ====================

    @commands.command(name='autotranslate')
    async def toggle_auto_translate(self, ctx):
        """Activa/desactiva la traducción automática (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        if channel not in translation_config:
            translation_config[channel] = {
                "auto_translate": True,
                "target_language": "es",
                "min_length": 10
            }

        # Alternar estado
        current_state = translation_config[channel]["auto_translate"]
        translation_config[channel]["auto_translate"] = not current_state

        status = "activada" if translation_config[channel]["auto_translate"] else "desactivada"
        await ctx.send(f"🌐 @{ctx.author.name} traducción automática {status}.")

    @commands.command(name='translang')
    async def set_translation_language(self, ctx):
        """Establece el idioma objetivo para traducción (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso: !translang <idioma>. Idiomas disponibles: es, en, fr, de, it, pt, ru")
            return

        target_lang = parts[1].lower()
        supported_langs = ["es", "en", "fr", "de", "it", "pt", "ru"]

        if target_lang not in supported_langs:
            await ctx.send(f"@{ctx.author.name} idioma no soportado. Disponibles: {', '.join(supported_langs)}")
            return

        channel = ctx.channel.name.lower()

        if channel not in translation_config:
            translation_config[channel] = {
                "auto_translate": True,
                "target_language": "es",
                "min_length": 10
            }

        translation_config[channel]["target_language"] = target_lang

        lang_names = {
            "es": "Español", "en": "Inglés", "fr": "Francés",
            "de": "Alemán", "it": "Italiano", "pt": "Portugués", "ru": "Ruso"
        }

        lang_name = lang_names.get(target_lang, target_lang.upper())
        await ctx.send(f"🌐 @{ctx.author.name} idioma objetivo cambiado a {lang_name}.")

    @commands.command(name='transinfo')
    async def translation_info(self, ctx):
        """Muestra información sobre la configuración de traducción"""
        channel = ctx.channel.name.lower()

        if channel not in translation_config:
            await ctx.send("La traducción automática no está configurada para este canal.")
            return

        config = translation_config[channel]
        status = "activada" if config["auto_translate"] else "desactivada"

        lang_names = {
            "es": "Español", "en": "Inglés", "fr": "Francés",
            "de": "Alemán", "it": "Italiano", "pt": "Portugués", "ru": "Ruso"
        }

        target_lang_name = lang_names.get(config["target_language"], config["target_language"].upper())

        info_text = (
            f"🌐 CONFIGURACIÓN DE TRADUCCIÓN:\n"
            f"Estado: {status}\n"
            f"Idioma objetivo: {target_lang_name}\n"
            f"Longitud mínima: {config['min_length']} caracteres\n"
            f"Idiomas detectables: Inglés, Francés, Alemán, Italiano, Portugués, Ruso"
        )

        await ctx.send(info_text)

    @commands.command(name='transminlength')
    async def set_translation_min_length(self, ctx):
        """Establece la longitud mínima para traducción automática (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 2:
            await ctx.send("Uso: !transminlength <número> (mínimo 3, máximo 50)")
            return

        try:
            min_length = int(parts[1])
            if min_length < 3 or min_length > 50:
                await ctx.send(f"@{ctx.author.name} la longitud debe estar entre 3 y 50 caracteres.")
                return
        except ValueError:
            await ctx.send(f"@{ctx.author.name} debes especificar un número válido.")
            return

        channel = ctx.channel.name.lower()

        if channel not in translation_config:
            translation_config[channel] = {
                "auto_translate": True,
                "target_language": "es",
                "min_length": min_length
            }
        else:
            translation_config[channel]["min_length"] = min_length

        await ctx.send(f"🔧 @{ctx.author.name} longitud mínima para traducción establecida en {min_length} caracteres.")

    @commands.command(name='detectlang')
    async def detect_language_command(self, ctx):
        """Detecta el idioma de un texto (para pruebas)"""
        parts = ctx.message.content.split(maxsplit=1)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !detectlang <texto>")
            return

        text = parts[1]
        detected = self.detect_language(text)

        lang_names = {
            "en": "🇺🇸 Inglés", "fr": "🇫🇷 Francés", "de": "🇩🇪 Alemán",
            "it": "🇮🇹 Italiano", "pt": "🇧🇷 Portugués", "ru": "🇷🇺 Ruso", "es": "🇪🇸 Español"
        }

        if detected:
            lang_name = lang_names.get(detected, detected.upper())
            await ctx.send(f"🔍 @{ctx.author.name} idioma detectado: {lang_name} ({detected})")
        else:
            await ctx.send(f"🔍 @{ctx.author.name} no se pudo detectar el idioma del texto.")

    @commands.command(name='tr')
    async def quick_translate(self, ctx):
        """Traducción rápida de texto"""
        parts = ctx.message.content.split(maxsplit=1)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !tr <texto>. El bot detectará el idioma automáticamente.")
            return

        text = parts[1]
        detected_lang = self.detect_language(text)

        if not detected_lang:
            await ctx.send(f"@{ctx.author.name} no se pudo detectar el idioma del texto.")
            return

        if detected_lang == "es":
            await ctx.send(f"@{ctx.author.name} el texto ya está en español.")
            return

        translated = self.translate_text(text, detected_lang, "es")

        if translated:
            lang_names = {
                "en": "Inglés", "fr": "Francés", "de": "Alemán",
                "it": "Italiano", "pt": "Portugués", "ru": "Ruso"
            }
            lang_name = lang_names.get(detected_lang, detected_lang.upper())
            await ctx.send(f"🌐 [{lang_name} → Español] @{ctx.author.name}: {translated}")
        else:
            await ctx.send(f"@{ctx.author.name} no se pudo traducir el texto. Intenta con palabras más comunes.")

    # ==================== COMANDOS OBS ====================

    @commands.command(name='obsupdate')
    async def update_obs_files(self, ctx):
        """Actualiza manualmente los archivos OBS (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()
        self.generate_obs_files(channel)
        await ctx.send(f"@{ctx.author.name} archivos OBS actualizados! 📁")

    @commands.command(name='obsinfo')
    async def obs_files_info(self, ctx):
        """Muestra información sobre los archivos OBS"""
        # Dividir en múltiples mensajes

        # Mensaje 1: Versiones completas
        msg1 = (
            "📁 ARCHIVOS OBS DISPONIBLES:\n"
            "🎨 VERSIÓN COMPLETA (decorada):\n"
            f"🔥 {ACTIVE_VIEWERS_FILE} - Top 10 espectadores\n"
            f"⭐ {TOP_CONTRIBUTORS_FILE} - Top 8 contribuidores\n"
            f"📊 {STREAM_STATS_FILE} - Estadísticas completas"
        )
        await ctx.send(msg1)

        # Mensaje 2: Versiones compactas
        msg2 = (
            "📱 VERSIÓN COMPACTA:\n"
            f"🔥 {COMPACT_VIEWERS_FILE} - Top 4 compacto\n"
            f"📊 {COMPACT_STATS_FILE} - Stats compactas\n"
            "📱 VERSIÓN MINI (ultra compacta):\n"
            f"🔥 {MINI_VIEWERS_FILE} - Top 3 mini\n"
            f"📊 {MINI_STATS_FILE} - Stats mini"
        )
        await ctx.send(msg2)

        # Mensaje 3: Comandos
        msg3 = (
            "⚙️ COMANDOS:\n"
            "!obsupdate - Actualizar normales\n"
            "!obscompact - Generar compactos\n"
            "!obsmini - Generar mini"
        )
        await ctx.send(msg3)

    @commands.command(name='topactive')
    async def show_top_active(self, ctx):
        """Muestra los espectadores más activos en el chat"""
        channel = ctx.channel.name.lower()

        if channel not in user_activity:
            await ctx.send("No hay datos de actividad disponibles aún.")
            return

        now = datetime.datetime.now()

        # Filtrar usuarios activos (últimos 30 minutos)
        active_users = {}
        for username, data in user_activity[channel].items():
            time_diff = now - data["last_activity"]
            if time_diff.total_seconds() <= 1800:  # 30 minutos
                active_users[username] = data

        if not active_users:
            await ctx.send("No hay usuarios activos en los últimos 30 minutos.")
            return

        # Ordenar por mensajes
        sorted_users = sorted(active_users.items(), key=lambda x: x[1]["messages"], reverse=True)

        top_text = "🔥 TOP 5 ESPECTADORES MÁS ACTIVOS:\n"
        for i, (username, data) in enumerate(sorted_users[:5], 1):
            messages = data["messages"]
            time_diff = now - data["last_activity"]
            minutes_ago = int(time_diff.total_seconds() / 60)

            if minutes_ago == 0:
                status = "🟢"
            elif minutes_ago <= 5:
                status = "🟡"
            else:
                status = "🔴"

            top_text += f"{i}. {status} @{username} - {messages} mensajes\n"

        await ctx.send(top_text)

    @commands.command(name='obscompact')
    async def generate_compact_obs(self, ctx):
        """Genera archivos OBS compactos (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        # Generar archivos compactos
        channel = ctx.channel.name.lower()
        self.generate_compact_obs_files(channel)

        await ctx.send(f"📁 @{ctx.author.name} archivos OBS compactos generados: compact_viewers.txt, compact_stats.txt")

    @commands.command(name='obsmini')
    async def generate_mini_obs(self, ctx):
        """Genera archivos OBS mini (ultra compactos) (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        # Generar archivos mini
        channel = ctx.channel.name.lower()
        self.generate_mini_obs_files(channel)

        await ctx.send(f"📁 @{ctx.author.name} archivos OBS mini generados: mini_viewers.txt, mini_stats.txt")

    @commands.command(name='obsreset')
    async def reset_obs_files(self, ctx):
        """Regenera todos los archivos OBS (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        # Regenerar todos los archivos
        channel = ctx.channel.name.lower()
        self.generate_obs_files(channel)
        self.generate_compact_obs_files(channel)
        self.generate_mini_obs_files(channel)

        await ctx.send(f"🔄 @{ctx.author.name} todos los archivos OBS han sido regenerados.")

    @commands.command(name='obsinfo')
    async def obs_info_command(self, ctx):
        """Muestra información sobre los archivos OBS disponibles"""
        info_text = (
            "📁 ARCHIVOS OBS DISPONIBLES:\n\n"
            "🎨 VERSIÓN NORMAL:\n"
            f"• {ACTIVE_VIEWERS_FILE} - Top 5 chat\n"
            f"• {TOP_CONTRIBUTORS_FILE} - Top 5 puntos\n"
            f"• {STREAM_STATS_FILE} - Info stream\n"
            f"• {NEXT_LEVELS_FILE} - Próximos niveles\n\n"
            "📦 VERSIÓN COMPACTA:\n"
            f"• {COMPACT_VIEWERS_FILE} - Top 3 chat\n"
            f"• {COMPACT_STATS_FILE} - Stats básicas\n\n"
            "🔹 VERSIÓN MINI:\n"
            f"• {MINI_VIEWERS_FILE} - Top 2 chat\n"
            f"• {MINI_STATS_FILE} - Solo esencial\n\n"
            "💡 Usa !obscompact o !obsmini para generar versiones específicas"
        )
        await ctx.send(info_text)

    @commands.command(name='obstest')
    async def test_obs_files(self, ctx):
        """Prueba que los archivos OBS se puedan leer correctamente"""
        channel = ctx.channel.name.lower()

        # Verificar que el directorio existe
        if not os.path.exists(OBS_FILES_DIR):
            await ctx.send(f"❌ @{ctx.author.name} directorio OBS no existe. Usa !obsreset para crearlo.")
            return

        # Lista de archivos a verificar
        files_to_check = [
            ACTIVE_VIEWERS_FILE,
            TOP_CONTRIBUTORS_FILE,
            STREAM_STATS_FILE,
            NEXT_LEVELS_FILE,
            COMPACT_VIEWERS_FILE,
            COMPACT_STATS_FILE,
            MINI_VIEWERS_FILE,
            MINI_STATS_FILE
        ]

        existing_files = []
        missing_files = []

        for filename in files_to_check:
            file_path = os.path.join(OBS_FILES_DIR, filename)
            if os.path.exists(file_path):
                existing_files.append(filename)
            else:
                missing_files.append(filename)

        result = f"📁 @{ctx.author.name} ESTADO ARCHIVOS OBS:\n"
        result += f"✅ Existentes: {len(existing_files)}\n"
        result += f"❌ Faltantes: {len(missing_files)}\n"

        if missing_files:
            result += f"Faltantes: {', '.join(missing_files[:3])}"
            if len(missing_files) > 3:
                result += f" y {len(missing_files)-3} más"

        await ctx.send(result)

    # ==================== COMANDOS DE TRADUCCIÓN ====================

    @commands.command(name='autotranslate')
    async def generate_mini_mode(self, ctx):
        """Genera archivos ultra compactos para OBS (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        # Generar solo archivos mini
        channel = ctx.channel.name.lower()

        if channel not in user_activity:
            active_users = {}
        else:
            now = datetime.datetime.now()
            active_users = {}
            for username, data in user_activity[channel].items():
                time_diff = now - data["last_activity"]
                if time_diff.total_seconds() <= 1800:  # 30 minutos
                    active_users[username] = data

        self.generate_mini_files(channel, active_users)
        await ctx.send(f"@{ctx.author.name} archivos OBS MINI generados! 📱 Ultra compactos para pantallas muy pequeñas.")

    # ==================== COMANDOS DE CHANNEL POINTS ====================

    @commands.command(name='channelpoints')
    async def channel_points_info(self, ctx):
        """Muestra información sobre las recompensas de Channel Points"""
        channel = ctx.channel.name.lower()

        if channel not in channel_points_config:
            await ctx.send("Las recompensas de Channel Points no están configuradas para este canal.")
            return

        config = channel_points_config[channel]
        status = "activadas" if config["enabled"] else "desactivadas"

        # Dividir en múltiples mensajes para evitar límite de 500 caracteres

        # Mensaje 1: Header y primeras recompensas
        msg1 = f"💎 RECOMPENSAS DE CHANNEL POINTS ({status}):\n"
        count = 0

        for _, reward_data in config["rewards"].items():
            name = reward_data["name"]
            cost = reward_data["cost"]
            action = reward_data["action"]

            # Emojis según el tipo de acción
            if action == "highlight":
                emoji = "✨"
            elif action == "skip_queue":
                emoji = "🚀"
            elif action == "choose_level":
                emoji = "👑"
            elif action == "random_level":
                emoji = "🎲"
            elif action == "add_bot_points":
                emoji = "💰"
            elif action == "trivia":
                emoji = "🧠"
            elif action == "free_raffle":
                emoji = "🎉"
            elif action == "custom_message":
                emoji = "📢"
            else:
                emoji = "⭐"

            line = f"{emoji} {name} - {cost:,} puntos\n"

            # Si agregar esta línea excedería 400 caracteres, enviar mensaje y empezar uno nuevo
            if len(msg1 + line) > 400:
                await ctx.send(msg1)
                msg1 = line
            else:
                msg1 += line

            count += 1

        # Enviar último mensaje
        if msg1:
            await ctx.send(msg1)

        # Mensaje final con información
        await ctx.send("💡 Usa tus Channel Points de Twitch para canjear estas recompensas!")

    @commands.command(name='redeem')
    async def manual_redeem(self, ctx):
        """Simula el canje de una recompensa de Channel Points"""
        parts = ctx.message.content.split(maxsplit=2)
        if len(parts) < 2:
            await ctx.send(f"@{ctx.author.name} uso: !redeem <nombre_recompensa> [parámetro]")
            return

        reward_name = parts[1]
        user_input = parts[2] if len(parts) > 2 else None

        channel = ctx.channel.name.lower()
        username = ctx.author.name.lower()

        result = self.process_channel_points_reward(channel, username, reward_name, user_input)

        if result:
            await ctx.send(result)
        else:
            await ctx.send(f"@{ctx.author.name} recompensa '{reward_name}' no encontrada o no disponible.")

    @commands.command(name='togglechannelpoints')
    async def toggle_channel_points(self, ctx):
        """Activa/desactiva las recompensas de Channel Points (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        channel = ctx.channel.name.lower()

        if channel not in channel_points_config:
            channel_points_config[channel] = {
                "enabled": True,
                "rewards": DEFAULT_CHANNEL_REWARDS.copy()
            }

        current_status = channel_points_config[channel]["enabled"]
        channel_points_config[channel]["enabled"] = not current_status

        status = "activadas" if channel_points_config[channel]["enabled"] else "desactivadas"
        await ctx.send(f"💎 @{ctx.author.name} recompensas de Channel Points {status}.")

    @commands.command(name='setrewardcost')
    async def set_reward_cost(self, ctx):
        """Cambia el costo de una recompensa (solo mods)"""
        if not self.has_permission(ctx):
            await ctx.send(f"@{ctx.author.name} no tienes permiso para usar este comando.")
            return

        parts = ctx.message.content.split()
        if len(parts) < 3:
            await ctx.send("Uso: !setrewardcost <nombre_recompensa> <nuevo_costo>")
            return

        reward_name = parts[1]
        try:
            new_cost = int(parts[2])
            if new_cost < 1:
                await ctx.send(f"@{ctx.author.name} el costo debe ser mayor a 0.")
                return
        except ValueError:
            await ctx.send(f"@{ctx.author.name} el costo debe ser un número válido.")
            return

        channel = ctx.channel.name.lower()

        if channel not in channel_points_config:
            await ctx.send("Las recompensas no están configuradas para este canal.")
            return

        # Buscar y actualizar la recompensa
        updated = False
        for reward_id, reward_data in channel_points_config[channel]["rewards"].items():
            if reward_data["name"].lower() == reward_name.lower():
                reward_data["cost"] = new_cost
                updated = True
                break

        if updated:
            await ctx.send(f"💎 @{ctx.author.name} costo de '{reward_name}' actualizado a {new_cost:,} puntos.")
        else:
            await ctx.send(f"@{ctx.author.name} recompensa '{reward_name}' no encontrada.")

def main():
    """Función principal para ejecutar el bot con reinicio automático"""
    print("Iniciando Paimon Bot...")
    print(f"Bot: {BOT_NICK}")
    print(f"Canales: {CHANNELS}")

    while True:
        bot = Bot()
        try:
            print("Conectando al bot...")
            bot.run()
        except asyncio.CancelledError:
            print("Bot cancelado por el usuario.")
            pass
        except KeyboardInterrupt:
            print("Bot interrumpido por el usuario.")
            break
        except Exception as e:
            print(f"Error inesperado en el bot: {e}")
            import traceback
            traceback.print_exc()

        if not bot.restart:
            print("Bot apagado.")
            break

        print("Reiniciando bot en 2 segundos...")
        time.sleep(2)  # Pausa de 2 segundos antes de reiniciar el bot

if __name__ == "__main__":
    main()
