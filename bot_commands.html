<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaiBot - Comandos y Estadísticas de Twitch</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.05);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite;
            text-shadow: none;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            color: #b8c6db;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 25px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.5);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .stat-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
        }

        .commands-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 2.2em;
            margin-bottom: 30px;
            text-align: center;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
        }

        .commands-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .command-category {
            background: rgba(255, 255, 255, 0.06);
            padding: 25px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .command-category::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .command-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
            border-color: rgba(255, 255, 255, 0.15);
        }

        .category-title {
            font-size: 1.4em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 8px;
        }

        .command-item {
            margin-bottom: 12px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border-left: 4px solid transparent;
            border-image: linear-gradient(45deg, #ff6b6b, #4ecdc4) 1;
            transition: all 0.3s ease;
            position: relative;
        }

        .command-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .command-name {
            font-weight: bold;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.05em;
        }

        .command-desc {
            font-size: 0.9em;
            opacity: 0.85;
            margin-top: 4px;
            color: #b8c6db;
        }

        .top-list {
            list-style: none;
        }

        .top-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border-left: 4px solid transparent;
            border-image: linear-gradient(45deg, #ffd700, #ff6b6b) 1;
            transition: all 0.3s ease;
        }

        .top-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .rank {
            font-weight: bold;
            background: linear-gradient(45deg, #ffd700, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-right: 12px;
            font-size: 1.1em;
        }

        .username {
            flex-grow: 1;
            font-weight: bold;
            color: #e8f4f8;
        }

        .stat-value {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 0.95em;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 4s ease-in-out infinite;
        }

        .update-time {
            font-size: 0.9em;
            opacity: 0.7;
            margin-top: 15px;
            color: #b8c6db;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .commands-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }

        .highlight {
            background: linear-gradient(45deg, #ff6b6b, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        /* Efectos adicionales para coincidencia con index.html */
        nav a:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            transform: translateY(-2px);
        }

        .footer a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* Animación de partículas de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.3) 0%, transparent 50%);
            z-index: -1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Efecto de brillo en hover para las tarjetas */
        .stat-card:hover, .command-category:hover {
            box-shadow:
                0 15px 45px rgba(0, 0, 0, 0.5),
                0 0 30px rgba(78, 205, 196, 0.2);
        }

        /* Scrollbar personalizada */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
        }
    </style>
</head>
<body>
    <!-- Navegación -->
    <nav style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(15px); padding: 15px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
        <div class="container" style="display: flex; justify-content: center; align-items: center; gap: 30px;">
            <a href="index.html" style="color: #b8c6db; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid transparent;">
                🏠 Inicio
            </a>
            <a href="bot_commands.html" style="color: #4ecdc4; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid rgba(78, 205, 196, 0.3); background: rgba(78, 205, 196, 0.1);">
                📋 Comandos
            </a>
            <a href="https://twitch.tv/flozwer" target="_blank" style="color: #b8c6db; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid transparent;">
                📺 Twitch
            </a>
        </div>
    </nav>

    <div class="container">
        <div class="header">
            <h1>🤖 PaiBot</h1>
            <p>Bot de Twitch para Geometry Dash - Comandos y Estadísticas en Tiempo Real</p>
        </div>

        <!-- Estadísticas en Tiempo Real -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><span class="emoji">🔥</span>Top Espectadores Activos</h3>
                <ul class="top-list" id="top-viewers">
                    <li class="top-item">
                        <span class="rank">1.</span>
                        <span class="username">Cargando...</span>
                        <span class="stat-value">-- msgs</span>
                    </li>
                </ul>
            </div>

            <div class="stat-card">
                <h3><span class="emoji">⭐</span>Top Contribuidores</h3>
                <ul class="top-list" id="top-contributors">
                    <li class="top-item">
                        <span class="rank">1.</span>
                        <span class="username">Cargando...</span>
                        <span class="stat-value">-- puntos</span>
                    </li>
                </ul>
            </div>

            <div class="stat-card">
                <h3><span class="emoji">📊</span>Estadísticas del Stream</h3>
                <div id="stream-stats">
                    <div class="command-item">
                        <div class="command-name">⏰ Tiempo Activo</div>
                        <div class="command-desc" id="uptime">Cargando...</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">👥 Espectadores Activos</div>
                        <div class="command-desc" id="active-viewers">Cargando...</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">🎮 Niveles en Cola</div>
                        <div class="command-desc" id="queue-count">Cargando...</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <h3><span class="emoji">🎯</span>Nivel Actual</h3>
                <div id="current-level">
                    <div class="command-item">
                        <div class="command-name">🎮 Nivel</div>
                        <div class="command-desc" id="level-name">Ningún nivel activo</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">👤 Autor</div>
                        <div class="command-desc" id="level-author">--</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">⚡ Dificultad</div>
                        <div class="command-desc" id="level-difficulty">--</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comandos por Categorías -->
        <div class="commands-section">
            <h2 class="section-title">📋 Comandos Disponibles</h2>
            
            <div class="commands-grid">
                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎮</span>Comandos Principales</h3>
                    <div class="command-item">
                        <div class="command-name">!add &lt;id&gt;</div>
                        <div class="command-desc">Agrega un nivel a la cola</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!lvl</div>
                        <div class="command-desc">Muestra el siguiente nivel (solo mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!queue</div>
                        <div class="command-desc">Ver toda la cola de niveles</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!P</div>
                        <div class="command-desc">Muestra tu posición en la cola</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!stats [usuario]</div>
                        <div class="command-desc">Estadísticas de niveles enviados</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!top</div>
                        <div class="command-desc">Top 5 usuarios con más niveles</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">💰</span>Sistema de Puntos</h3>
                    <div class="command-item">
                        <div class="command-name">!points</div>
                        <div class="command-desc">Ver tus puntos actuales</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!daily</div>
                        <div class="command-desc">Reclamar puntos diarios (50-100)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!shop</div>
                        <div class="command-desc">Ver tienda de recompensas</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!buy &lt;item&gt;</div>
                        <div class="command-desc">Comprar items de la tienda</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎉</span>Sorteos</h3>
                    <div class="command-item">
                        <div class="command-name">!sorteo &lt;premio&gt; [duración]</div>
                        <div class="command-desc">Iniciar sorteo (solo mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!participar</div>
                        <div class="command-desc">Unirse al sorteo activo</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!ganador</div>
                        <div class="command-desc">Seleccionar ganador (solo mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!sorteoinfo</div>
                        <div class="command-desc">Info del sorteo activo</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎮</span>Juegos de Chat</h3>
                    <div class="command-item">
                        <div class="command-name">!8ball &lt;pregunta&gt;</div>
                        <div class="command-desc">Bola mágica 8 con respuestas aleatorias</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!roll [número]</div>
                        <div class="command-desc">Tirar dados (1-100 por defecto)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!flip</div>
                        <div class="command-desc">Lanzar moneda (cara o cruz)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!rps &lt;opción&gt;</div>
                        <div class="command-desc">Piedra, papel o tijera contra el bot</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!trivia</div>
                        <div class="command-desc">Preguntas sobre Geometry Dash</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!quote</div>
                        <div class="command-desc">Frases inspiradoras aleatorias</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">😄</span>Comandos Divertidos</h3>
                    <div class="command-item">
                        <div class="command-name">!hug &lt;usuario&gt;</div>
                        <div class="command-desc">Abrazar a otro usuario</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!slap &lt;usuario&gt;</div>
                        <div class="command-desc">Golpear juguetonamente</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!love &lt;usuario&gt;</div>
                        <div class="command-desc">Mostrar amor hacia otro usuario</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!ship &lt;user1&gt; &lt;user2&gt;</div>
                        <div class="command-desc">Calcular compatibilidad entre usuarios</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!fact</div>
                        <div class="command-desc">Datos curiosos aleatorios</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">📊</span>Información del Stream</h3>
                    <div class="command-item">
                        <div class="command-name">!uptime</div>
                        <div class="command-desc">Tiempo que lleva el bot activo</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!viewers</div>
                        <div class="command-desc">Número actual de viewers</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!chatters</div>
                        <div class="command-desc">Usuarios activos en chat</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!followage [usuario]</div>
                        <div class="command-desc">Tiempo siguiendo el canal</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!topactive</div>
                        <div class="command-desc">Top 5 espectadores más activos</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🌐</span>Traducción Automática</h3>
                    <div class="command-item">
                        <div class="command-name">!translate &lt;idioma&gt; &lt;texto&gt;</div>
                        <div class="command-desc">Traducir texto manualmente</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!tr &lt;texto&gt;</div>
                        <div class="command-desc">Traducción rápida automática</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!detectlang &lt;texto&gt;</div>
                        <div class="command-desc">Detectar idioma de un texto</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!transinfo</div>
                        <div class="command-desc">Info de configuración de traducción</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Traducción Automática</div>
                        <div class="command-desc">El bot traduce automáticamente mensajes en otros idiomas</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">💎</span>Channel Points</h3>
                    <div class="command-item">
                        <div class="command-name">!channelpoints</div>
                        <div class="command-desc">Ver recompensas disponibles</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!redeem &lt;recompensa&gt;</div>
                        <div class="command-desc">Simular canje de recompensa</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Destacar Mensaje (500 pts)</div>
                        <div class="command-desc">Tu próximo mensaje será destacado</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Saltarse Cola (1000 pts)</div>
                        <div class="command-desc">Mover tu nivel al frente</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Elegir Próximo Nivel (1500 pts)</div>
                        <div class="command-desc">Agregar nivel específico como próximo</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">📁</span>Archivos OBS</h3>
                    <div class="command-item">
                        <div class="command-name">!obsinfo</div>
                        <div class="command-desc">Información sobre archivos OBS</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!obsupdate</div>
                        <div class="command-desc">Actualizar archivos manualmente (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!obscompact</div>
                        <div class="command-desc">Generar versiones compactas (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!obsmini</div>
                        <div class="command-desc">Generar versiones mini (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Actualización Automática</div>
                        <div class="command-desc">Los archivos se actualizan cada 10 mensajes</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🛠️</span>Utilidades</h3>
                    <div class="command-item">
                        <div class="command-name">!time [zona]</div>
                        <div class="command-desc">Hora actual en diferentes zonas</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!calc &lt;operación&gt;</div>
                        <div class="command-desc">Calculadora simple</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!weather [ciudad]</div>
                        <div class="command-desc">Información del clima</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!color &lt;hex&gt;</div>
                        <div class="command-desc">Información de color hexadecimal</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!schedule</div>
                        <div class="command-desc">Horario de streams</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎯</span>Geometry Dash</h3>
                    <div class="command-item">
                        <div class="command-name">!search &lt;nombre&gt;</div>
                        <div class="command-desc">Buscar niveles por nombre</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!profile &lt;usuario_gd&gt;</div>
                        <div class="command-desc">Perfil de usuario de GD</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!featured</div>
                        <div class="command-desc">Niveles destacados recientes</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!geometry</div>
                        <div class="command-desc">Tips y trucos de Geometry Dash</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!creator</div>
                        <div class="command-desc">Consejos para creators</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">⚙️</span>Moderación</h3>
                    <div class="command-item">
                        <div class="command-name">!remove &lt;posición&gt;</div>
                        <div class="command-desc">Remover nivel de la cola (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!clear</div>
                        <div class="command-desc">Limpiar toda la cola (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!skip</div>
                        <div class="command-desc">Saltar nivel actual (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!give &lt;user&gt; &lt;puntos&gt;</div>
                        <div class="command-desc">Dar puntos a usuario (mods)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">!per &lt;usuario&gt;</div>
                        <div class="command-desc">Dar permisos (solo streamer)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Información Adicional -->
        <div class="commands-section">
            <h2 class="section-title">ℹ️ Información Adicional</h2>

            <div class="commands-grid">
                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎮</span>Sobre el Bot</h3>
                    <div class="command-item">
                        <div class="command-name">Nombre</div>
                        <div class="command-desc">Paimon Bot - Asistente de Twitch para Geometry Dash</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Desarrollado para</div>
                        <div class="command-desc">FlozWer - Streamer de Geometry Dash</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Funcionalidades</div>
                        <div class="command-desc">Gestión de niveles, sorteos, puntos, traducción automática, OBS</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Idiomas Soportados</div>
                        <div class="command-desc">Español, Inglés, Francés, Alemán, Italiano, Portugués, Ruso</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">📊</span>Estadísticas del Bot</h3>
                    <div class="command-item">
                        <div class="command-name">Total de Comandos</div>
                        <div class="command-desc">80+ comandos disponibles</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Categorías</div>
                        <div class="command-desc">12 categorías diferentes</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Archivos OBS</div>
                        <div class="command-desc">7 archivos diferentes (normal, compacto, mini)</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Channel Points</div>
                        <div class="command-desc">8 recompensas únicas disponibles</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🔗</span>Enlaces Útiles</h3>
                    <div class="command-item">
                        <div class="command-name">Canal de Twitch</div>
                        <div class="command-desc">twitch.tv/flozwer</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">GitHub del Bot</div>
                        <div class="command-desc">Código fuente y documentación</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">GDBrowser API</div>
                        <div class="command-desc">gdbrowser.com - API para información de niveles</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">Esta Página</div>
                        <div class="command-desc">Se actualiza automáticamente con datos del bot</div>
                    </div>
                </div>

                <div class="command-category">
                    <h3 class="category-title"><span class="emoji">🎯</span>Comandos Más Usados</h3>
                    <div class="command-item">
                        <div class="command-name">1. !add</div>
                        <div class="command-desc">Enviar niveles a la cola</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">2. !P</div>
                        <div class="command-desc">Ver posición en la cola</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">3. !daily</div>
                        <div class="command-desc">Reclamar puntos diarios</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">4. !participar</div>
                        <div class="command-desc">Unirse a sorteos</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">5. !points</div>
                        <div class="command-desc">Ver puntos actuales</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Base de datos simulada más amplia
        const viewersDatabase = [
            {name: "flozwer", messages: 45, status: "🟢"},
            {name: "usuario123", messages: 32, status: "🟡"},
            {name: "gamer_pro", messages: 28, status: "🟢"},
            {name: "viewer_cool", messages: 21, status: "🟡"},
            {name: "chat_master", messages: 18, status: "🔴"},
            {name: "demon_hunter", messages: 15, status: "🟢"},
            {name: "level_creator", messages: 12, status: "🟡"},
            {name: "gd_fan2023", messages: 10, status: "🟢"}
        ];

        const contributorsDatabase = [
            {name: "flozwer", points: 1250, levels: 15},
            {name: "top_player", points: 890, levels: 12},
            {name: "gd_master", points: 675, levels: 8},
            {name: "level_king", points: 543, levels: 6},
            {name: "demon_slayer", points: 432, levels: 5},
            {name: "creator_pro", points: 387, levels: 4},
            {name: "geometry_god", points: 298, levels: 3},
            {name: "dash_expert", points: 234, levels: 2}
        ];

        const levelsDatabase = [
            {name: "Bloodbath", author: "Riot", difficulty: "Extreme Demon", id: "10565740"},
            {name: "Sonic Wave", author: "Cyclic", difficulty: "Extreme Demon", id: "26681070"},
            {name: "Cataclysm", author: "Ggb0y", difficulty: "Extreme Demon", id: "3979721"},
            {name: "The Golden", author: "BoBoBoBoBoBo", difficulty: "Extreme Demon", id: "23459013"},
            {name: "Artificial Ascent", author: "Viprin", difficulty: "Hard Demon", id: "28220417"},
            {name: "Deadlocked", author: "RobTop", difficulty: "Hard Demon", id: "10565740"},
            {name: "Theory of Everything 2", author: "RobTop", difficulty: "Hard", id: "10565741"}
        ];

        let currentLevelIndex = 0;
        let streamStartTime = new Date();

        // Simulación de datos en tiempo real más realista
        function updateStats() {
            // Simular cambios en mensajes (algunos usuarios escriben más)
            viewersDatabase.forEach(viewer => {
                if (Math.random() < 0.3) { // 30% chance de que escriba
                    viewer.messages += Math.floor(Math.random() * 3) + 1;
                }
            });

            // Ordenar por mensajes y tomar top 5
            const topViewers = [...viewersDatabase]
                .sort((a, b) => b.messages - a.messages)
                .slice(0, 5);

            const viewersList = document.getElementById('top-viewers');
            viewersList.innerHTML = topViewers.map((viewer, index) => `
                <li class="top-item">
                    <span class="rank">${index + 1}.</span>
                    <span class="username">${viewer.status} ${viewer.name}</span>
                    <span class="stat-value">${viewer.messages} msgs</span>
                </li>
            `).join('');

            // Simular cambios en puntos
            contributorsDatabase.forEach(contributor => {
                if (Math.random() < 0.1) { // 10% chance de ganar puntos
                    contributor.points += Math.floor(Math.random() * 20) + 5;
                }
            });

            // Ordenar por puntos y tomar top 5
            const topContributors = [...contributorsDatabase]
                .sort((a, b) => b.points - a.points)
                .slice(0, 5);

            const contributorsList = document.getElementById('top-contributors');
            contributorsList.innerHTML = topContributors.map((contributor, index) => {
                const medals = ["👑", "🥈", "🥉", "⭐", "💫"];
                const medal = medals[index] || "💫";
                return `
                    <li class="top-item">
                        <span class="rank">${medal}</span>
                        <span class="username">${contributor.name}</span>
                        <span class="stat-value">${contributor.points} pts</span>
                    </li>
                `;
            }).join('');

            // Calcular uptime real
            const now = new Date();
            const uptimeMs = now - streamStartTime;
            const hours = Math.floor(uptimeMs / (1000 * 60 * 60));
            const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));

            document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;

            // Simular viewers activos (fluctúa entre 5-15)
            const activeViewers = Math.floor(Math.random() * 11) + 5;
            document.getElementById('active-viewers').textContent = `${activeViewers} espectadores`;

            // Simular cola de niveles (fluctúa entre 0-8)
            const queueCount = Math.floor(Math.random() * 9);
            document.getElementById('queue-count').textContent = `${queueCount} niveles`;

            // Cambiar nivel actual ocasionalmente
            if (Math.random() < 0.05) { // 5% chance de cambiar nivel
                currentLevelIndex = (currentLevelIndex + 1) % levelsDatabase.length;
            }

            const currentLevel = levelsDatabase[currentLevelIndex];
            document.getElementById('level-name').textContent = currentLevel.name;
            document.getElementById('level-author').textContent = currentLevel.author;
            document.getElementById('level-difficulty').textContent = currentLevel.difficulty;
        }

        // Función para simular actividad más realista
        function simulateActivity() {
            // Simular nuevos usuarios uniéndose ocasionalmente
            if (Math.random() < 0.02) { // 2% chance
                const newUserNames = ["new_viewer", "gd_newbie", "first_time", "curious_user", "random_player"];
                const randomName = newUserNames[Math.floor(Math.random() * newUserNames.length)] + Math.floor(Math.random() * 1000);

                if (viewersDatabase.length < 15) {
                    viewersDatabase.push({
                        name: randomName,
                        messages: Math.floor(Math.random() * 5) + 1,
                        status: "🟢"
                    });
                }
            }

            // Simular usuarios volviéndose inactivos
            viewersDatabase.forEach(viewer => {
                if (Math.random() < 0.01) { // 1% chance
                    viewer.status = viewer.status === "🟢" ? "🟡" : (viewer.status === "🟡" ? "🔴" : "🟢");
                }
            });
        }

        // Mostrar hora de última actualización
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES');
            const dateString = now.toLocaleDateString('es-ES');
            document.querySelector('.update-time').textContent = `Última actualización: ${dateString} ${timeString}`;
        }

        // Función para agregar efectos visuales
        function addVisualEffects() {
            // Agregar efecto de parpadeo a elementos activos
            const activeElements = document.querySelectorAll('.stat-card');
            activeElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        element.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        }

        // Inicializar
        updateStats();
        updateTime();

        // Intervalos de actualización
        setInterval(updateStats, 15000); // Cada 15 segundos
        setInterval(simulateActivity, 30000); // Cada 30 segundos
        setInterval(updateTime, 1000); // Cada segundo
        setInterval(addVisualEffects, 60000); // Cada minuto

        // Agregar interactividad
        document.addEventListener('DOMContentLoaded', function() {
            // Hacer que las tarjetas sean clickeables
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Agregar tooltips a los comandos
            const commandItems = document.querySelectorAll('.command-item');
            commandItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                });
            });
        });

        // Función para mostrar notificaciones simuladas
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Simular notificaciones ocasionales
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance cada minuto
                const notifications = [
                    "🎉 Nuevo nivel agregado a la cola!",
                    "💰 Usuario reclamó puntos diarios!",
                    "🎮 Nivel completado!",
                    "🏆 Nuevo sorteo iniciado!",
                    "⭐ Usuario ganó recompensa!"
                ];
                const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
                showNotification(randomNotification);
            }
        }, 60000); // Cada minuto
    </script>

    <div class="footer">
        <p style="font-size: 1.1em; margin-bottom: 10px;">
            🤖 <span style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: bold;">PaiBot</span>
            - Desarrollado para la comunidad de
            <span style="background: linear-gradient(45deg, #4ecdc4, #45b7d1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: bold;">FlozWer</span>
        </p>
        <p style="font-size: 0.9em; opacity: 0.7; margin-bottom: 15px;">
            Bot de Twitch para Geometry Dash con más de 80 comandos disponibles
        </p>
        <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
            <a href="https://twitch.tv/flozwer" target="_blank" style="color: #9146ff; text-decoration: none; padding: 8px 16px; border-radius: 8px; background: rgba(145, 70, 255, 0.1); border: 1px solid rgba(145, 70, 255, 0.3); transition: all 0.3s ease;">
                📺 Twitch
            </a>
            <a href="https://github.com/Fl0zWer/Paimon" target="_blank" style="color: #4ecdc4; text-decoration: none; padding: 8px 16px; border-radius: 8px; background: rgba(78, 205, 196, 0.1); border: 1px solid rgba(78, 205, 196, 0.3); transition: all 0.3s ease;">
                💻 GitHub
            </a>
        </div>
        <p class="update-time">Cargando...</p>
    </div>
</body>
</html>
