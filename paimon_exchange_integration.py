#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Paimon Bot - Integración de Sistema de Canjes
Módulo para integrar los canjes configurados con el bot principal
"""

import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple

class PaimonExchangeSystem:
    def __init__(self, config_file: str = ".Paimon"):
        """
        Inicializa el sistema de canjes
        
        Args:
            config_file: Ruta al archivo de configuración .Paimon
        """
        self.config_file = config_file
        self.config = {}
        self.user_cooldowns = {}  # {user_id: {exchange_id: datetime}}
        self.exchange_usage = {}  # {exchange_id: usage_count}
        
        self.load_config()
    
    def load_config(self) -> bool:
        """
        Carga la configuración desde el archivo .Paimon
        
        Returns:
            bool: True si se cargó correctamente, False en caso contrario
        """
        try:
            if not os.path.exists(self.config_file):
                print(f"❌ Archivo de configuración {self.config_file} no encontrado")
                return False
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            print(f"✅ Configuración de canjes cargada desde {self.config_file}")
            print(f"📊 {len(self.config.get('exchanges', {}))} canjes disponibles")
            return True
            
        except Exception as e:
            print(f"❌ Error cargando configuración: {e}")
            return False
    
    def get_active_exchanges(self) -> Dict:
        """
        Obtiene todos los canjes activos
        
        Returns:
            Dict: Diccionario con los canjes activos
        """
        if not self.config or 'exchanges' not in self.config:
            return {}
        
        return {
            exchange_id: exchange 
            for exchange_id, exchange in self.config['exchanges'].items()
            if exchange.get('active', False)
        }
    
    def get_exchanges_by_category(self, category: str) -> Dict:
        """
        Obtiene canjes por categoría
        
        Args:
            category: Categoría a filtrar
            
        Returns:
            Dict: Canjes de la categoría especificada
        """
        active_exchanges = self.get_active_exchanges()
        return {
            exchange_id: exchange
            for exchange_id, exchange in active_exchanges.items()
            if exchange.get('category', 'General').lower() == category.lower()
        }
    
    def can_user_exchange(self, user_id: str, exchange_id: str) -> Tuple[bool, str]:
        """
        Verifica si un usuario puede realizar un canje
        
        Args:
            user_id: ID del usuario
            exchange_id: ID del canje
            
        Returns:
            Tuple[bool, str]: (puede_canjear, mensaje_error)
        """
        # Verificar si el canje existe y está activo
        active_exchanges = self.get_active_exchanges()
        if exchange_id not in active_exchanges:
            return False, "Canje no encontrado o no disponible"
        
        exchange = active_exchanges[exchange_id]
        
        # Verificar cooldown
        if self._is_user_on_cooldown(user_id, exchange_id):
            cooldown_time = exchange.get('cooldown', 0)
            return False, f"Debes esperar {cooldown_time} segundos antes de usar este canje de nuevo"
        
        # Verificar usos máximos
        max_uses = exchange.get('max_uses', 0)
        if max_uses > 0:
            current_uses = self.exchange_usage.get(exchange_id, 0)
            if current_uses >= max_uses:
                return False, "Este canje ha alcanzado su límite de usos"
        
        return True, ""
    
    def process_exchange(self, user_id: str, exchange_id: str, user_points: int) -> Tuple[bool, str, int]:
        """
        Procesa un canje de usuario
        
        Args:
            user_id: ID del usuario
            exchange_id: ID del canje
            user_points: Puntos actuales del usuario
            
        Returns:
            Tuple[bool, str, int]: (éxito, mensaje, puntos_restantes)
        """
        # Verificar si puede canjear
        can_exchange, error_msg = self.can_user_exchange(user_id, exchange_id)
        if not can_exchange:
            return False, error_msg, user_points
        
        active_exchanges = self.get_active_exchanges()
        exchange = active_exchanges[exchange_id]
        cost = exchange['cost']
        
        # Verificar si tiene suficientes puntos
        if user_points < cost:
            currency_symbol = self.config.get('currency_symbol', '💎')
            return False, f"No tienes suficientes puntos. Necesitas {cost} {currency_symbol}, tienes {user_points}", user_points
        
        # Procesar canje
        remaining_points = user_points - cost
        
        # Registrar cooldown
        self._set_user_cooldown(user_id, exchange_id)
        
        # Incrementar contador de usos
        self.exchange_usage[exchange_id] = self.exchange_usage.get(exchange_id, 0) + 1
        
        # Mensaje de éxito
        currency_symbol = self.config.get('currency_symbol', '💎')
        success_msg = f"✅ Canje '{exchange['name']}' realizado! -{cost} {currency_symbol} (Restantes: {remaining_points})"
        
        return True, success_msg, remaining_points
    
    def get_shop_display(self, category: Optional[str] = None) -> List[str]:
        """
        Genera la visualización de la tienda
        
        Args:
            category: Categoría específica a mostrar (opcional)
            
        Returns:
            List[str]: Lista de líneas para mostrar la tienda
        """
        if category:
            exchanges = self.get_exchanges_by_category(category)
            title = f"🏪 TIENDA - {category.upper()}"
        else:
            exchanges = self.get_active_exchanges()
            title = "🏪 TIENDA DE CANJES"
        
        if not exchanges:
            return [title, "No hay canjes disponibles"]
        
        lines = [title]
        currency_symbol = self.config.get('currency_symbol', '💎')
        
        # Agrupar por categoría si no se especificó una
        if not category:
            categories = {}
            for exchange_id, exchange in exchanges.items():
                cat = exchange.get('category', 'General')
                if cat not in categories:
                    categories[cat] = []
                categories[cat].append((exchange_id, exchange))
            
            for cat_name, cat_exchanges in categories.items():
                lines.append(f"\n📂 {cat_name.upper()}:")
                for exchange_id, exchange in cat_exchanges:
                    cost = exchange['cost']
                    name = exchange['name']
                    lines.append(f"  • {name}: {cost} {currency_symbol}")
        else:
            for exchange_id, exchange in exchanges.items():
                cost = exchange['cost']
                name = exchange['name']
                lines.append(f"• {name}: {cost} {currency_symbol}")
        
        lines.append(f"\nUsa: !canje <nombre> para comprar")
        return lines
    
    def find_exchange_by_name(self, name: str) -> Optional[Tuple[str, Dict]]:
        """
        Busca un canje por nombre (búsqueda flexible)
        
        Args:
            name: Nombre del canje a buscar
            
        Returns:
            Optional[Tuple[str, Dict]]: (exchange_id, exchange_data) o None
        """
        active_exchanges = self.get_active_exchanges()
        name_lower = name.lower().strip()
        
        # Búsqueda exacta
        for exchange_id, exchange in active_exchanges.items():
            if exchange['name'].lower() == name_lower:
                return exchange_id, exchange
        
        # Búsqueda parcial
        for exchange_id, exchange in active_exchanges.items():
            if name_lower in exchange['name'].lower():
                return exchange_id, exchange
        
        return None
    
    def get_exchange_info(self, exchange_id: str) -> Optional[str]:
        """
        Obtiene información detallada de un canje
        
        Args:
            exchange_id: ID del canje
            
        Returns:
            Optional[str]: Información del canje o None
        """
        active_exchanges = self.get_active_exchanges()
        if exchange_id not in active_exchanges:
            return None
        
        exchange = active_exchanges[exchange_id]
        currency_symbol = self.config.get('currency_symbol', '💎')
        
        info_lines = [
            f"📋 {exchange['name']}",
            f"💰 Costo: {exchange['cost']} {currency_symbol}",
            f"📂 Categoría: {exchange.get('category', 'General')}",
            f"📝 {exchange['description']}"
        ]
        
        # Información adicional
        cooldown = exchange.get('cooldown', 0)
        if cooldown > 0:
            info_lines.append(f"⏱️ Tiempo de espera: {cooldown} segundos")
        
        max_uses = exchange.get('max_uses', 0)
        if max_uses > 0:
            current_uses = self.exchange_usage.get(exchange_id, 0)
            info_lines.append(f"🔢 Usos: {current_uses}/{max_uses}")
        
        return "\n".join(info_lines)
    
    def _is_user_on_cooldown(self, user_id: str, exchange_id: str) -> bool:
        """Verifica si el usuario está en cooldown para un canje específico"""
        if user_id not in self.user_cooldowns:
            return False
        
        if exchange_id not in self.user_cooldowns[user_id]:
            return False
        
        active_exchanges = self.get_active_exchanges()
        if exchange_id not in active_exchanges:
            return False
        
        cooldown_seconds = active_exchanges[exchange_id].get('cooldown', 0)
        if cooldown_seconds <= 0:
            return False
        
        last_use = self.user_cooldowns[user_id][exchange_id]
        cooldown_end = last_use + timedelta(seconds=cooldown_seconds)
        
        return datetime.now() < cooldown_end
    
    def _set_user_cooldown(self, user_id: str, exchange_id: str):
        """Establece el cooldown para un usuario y canje específico"""
        if user_id not in self.user_cooldowns:
            self.user_cooldowns[user_id] = {}
        
        self.user_cooldowns[user_id][exchange_id] = datetime.now()
    
    def get_config_info(self) -> Dict:
        """
        Obtiene información de la configuración actual
        
        Returns:
            Dict: Información de configuración
        """
        if not self.config:
            return {}
        
        return {
            'bot_name': self.config.get('bot_name', 'Paimon'),
            'currency_name': self.config.get('currency_name', 'Puntos'),
            'currency_symbol': self.config.get('currency_symbol', '💎'),
            'total_exchanges': len(self.config.get('exchanges', {})),
            'active_exchanges': len(self.get_active_exchanges()),
            'version': self.config.get('version', '1.0'),
            'last_modified': self.config.get('last_modified', 'N/A')
        }

# Ejemplo de uso
if __name__ == "__main__":
    print("🌟 Probando Sistema de Canjes de Paimon")
    print("=" * 50)
    
    # Inicializar sistema
    exchange_system = PaimonExchangeSystem()
    
    # Mostrar información
    config_info = exchange_system.get_config_info()
    print(f"Bot: {config_info['bot_name']}")
    print(f"Moneda: {config_info['currency_symbol']} {config_info['currency_name']}")
    print(f"Canjes activos: {config_info['active_exchanges']}/{config_info['total_exchanges']}")
    
    # Mostrar tienda
    print("\n" + "\n".join(exchange_system.get_shop_display()))
    
    # Ejemplo de canje
    print(f"\n🧪 Simulando canje...")
    success, message, remaining = exchange_system.process_exchange("test_user", "12345678", 200)
    print(f"Resultado: {message}")
    print(f"Puntos restantes: {remaining}")
