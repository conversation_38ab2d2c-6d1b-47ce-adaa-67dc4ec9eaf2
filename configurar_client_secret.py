#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para configurar el Client Secret en PBot.py
"""

import os
import webbrowser

def open_twitch_console():
    """Abre la consola de desarrolladores de Twitch"""
    url = "https://dev.twitch.tv/console/apps"
    print("🌐 Abriendo Twitch Developer Console...")
    try:
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"❌ Error abriendo navegador: {e}")
        print(f"📋 Ve manualmente a: {url}")
        return False

def show_instructions():
    """Muestra las instrucciones para obtener Client Secret"""
    print("🔧 CÓMO OBTENER CLIENT SECRET:")
    print("=" * 60)
    print("1. 🌐 Ve a: https://dev.twitch.tv/console/apps")
    print("2. 🔍 Busca tu aplicación 'Paimon Bot' o crea una nueva")
    print("3. 🖱️  Haz clic en 'Manage' en tu aplicación")
    print("4. 🔑 En la sección 'Client Secret', haz clic en 'New Secret'")
    print("5. 📋 Copia el Client Secret que aparece")
    print("6. ⚠️  IMPORTANTE: Guárdalo bien, solo se muestra una vez")
    print("")
    print("📝 CONFIGURACIÓN DE LA APLICACIÓN:")
    print("- Nombre: Paimon Bot")
    print("- OAuth Redirect URLs: http://localhost:3000")
    print("- Categoría: Chat Bot")
    print("=" * 60)

def update_client_secret(client_secret):
    """Actualiza el Client Secret en PBot.py"""
    try:
        # Leer archivo actual
        with open('PBot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Reemplazar CLIENT_SECRET
        old_line = "CLIENT_SECRET = 'tu_client_secret_aqui'"
        new_line = f"CLIENT_SECRET = '{client_secret}'"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Guardar archivo
            with open('PBot.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ ¡Client Secret configurado exitosamente!")
            print(f"🔑 CLIENT_SECRET actualizado en PBot.py")
            return True
        else:
            print("⚠️ No se encontró la línea a reemplazar")
            print("📝 Actualiza manualmente la línea 20 en PBot.py:")
            print(f"CLIENT_SECRET = '{client_secret}'")
            return False
            
    except FileNotFoundError:
        print("❌ Archivo PBot.py no encontrado")
        print("📁 Asegúrate de estar en el directorio correcto")
        return False
    except Exception as e:
        print(f"❌ Error actualizando archivo: {e}")
        return False

def verify_configuration():
    """Verifica que toda la configuración esté completa"""
    try:
        with open('PBot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar valores configurados
        has_client_id = "CLIENT_ID = 'u6yahoy75fczq61cvvdiflxjlv4605'" in content
        has_bot_id = "BOT_ID = '763656753'" in content
        has_client_secret = "CLIENT_SECRET = 'tu_client_secret_aqui'" not in content
        
        print("🔍 VERIFICACIÓN DE CONFIGURACIÓN:")
        print("=" * 40)
        print(f"✅ Client ID: {'Configurado' if has_client_id else '❌ Falta'}")
        print(f"✅ Bot ID: {'Configurado' if has_bot_id else '❌ Falta'}")
        print(f"✅ Client Secret: {'Configurado' if has_client_secret else '❌ Falta'}")
        print("=" * 40)
        
        if has_client_id and has_bot_id and has_client_secret:
            print("🎉 ¡CONFIGURACIÓN COMPLETA!")
            print("🚀 Puedes ejecutar: python PBot.py")
            return True
        else:
            print("⚠️ Configuración incompleta")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando configuración: {e}")
        return False

def main():
    """Función principal"""
    print("🔑 Configurador de Client Secret - Paimon Bot")
    print("=" * 60)
    
    # Verificar configuración actual
    print("📋 Estado actual:")
    verify_configuration()
    print("")
    
    # Mostrar instrucciones
    show_instructions()
    print("")
    
    # Preguntar si quiere abrir la consola
    try:
        print("🌐 ¿Quieres que abra Twitch Developer Console? (s/n): ", end="")
        respuesta = input().lower().strip()
        
        if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
            open_twitch_console()
            print("✅ Consola abierta en el navegador")
        
        print("")
        print("🔑 Ingresa tu Client Secret (o 'salir' para cancelar): ", end="")
        client_secret = input().strip()
        
        if client_secret.lower() in ['salir', 'exit', 'quit', '']:
            print("👋 Configuración cancelada")
            return
        
        # Validar que no esté vacío y tenga formato correcto
        if len(client_secret) < 10:
            print("⚠️ El Client Secret parece muy corto")
            print("🔍 Verifica que hayas copiado el valor completo")
            return
        
        # Actualizar archivo
        if update_client_secret(client_secret):
            print("")
            print("🔍 Verificando configuración final...")
            if verify_configuration():
                print("")
                print("🎉 ¡CONFIGURACIÓN COMPLETADA!")
                print("🚀 Ahora puedes ejecutar tu bot:")
                print("   python PBot.py")
                print("")
                print("💡 O usar el bot integrado:")
                print("   python paimon_bot_integrated.py")
            else:
                print("⚠️ Hay problemas con la configuración")
        
    except KeyboardInterrupt:
        print("\n👋 Configuración cancelada")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")

if __name__ == "__main__":
    main()
