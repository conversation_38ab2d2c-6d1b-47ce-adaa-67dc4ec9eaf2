#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pa<PERSON>n Bot - Gestor de Configuración de Canjes
Interfaz gráfica para gestionar canjes de monedas del bot
Archivo de configuración: .Paimon
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
import uuid

class PaimonConfigManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🌟 Paimon Bot - Gestor de Canjes")
        self.root.geometry("900x700")
        self.root.configure(bg='#0f0f1a')
        
        # Configuración por defecto
        self.config = {
            "bot_name": "Paimon",
            "currency_name": "Primogemas",
            "currency_symbol": "💎",
            "exchanges": {},
            "settings": {
                "min_exchange_cost": 10,
                "max_exchange_cost": 10000,
                "allow_custom_exchanges": True,
                "auto_save": True,
                "backup_enabled": True
            },
            "created_date": datetime.now().isoformat(),
            "last_modified": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        self.config_file = ".Paimon"
        self.load_config()
        self.setup_ui()
        
    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Estilo
        style = ttk.Style()
        style.theme_use('clam')

        # Configurar tema oscuro completo estilo Genshin Impact
        style.configure('Title.TLabel',
                       background='#0f0f1a',
                       foreground='#ffd700',
                       font=('Arial', 16, 'bold'))

        style.configure('Header.TLabel',
                       background='#0f0f1a',
                       foreground='#e8e8e8',
                       font=('Arial', 12, 'bold'))

        style.configure('Custom.TFrame', background='#0f0f1a')

        # Configurar Notebook (pestañas) oscuro
        style.configure('TNotebook', background='#0f0f1a', borderwidth=0)
        style.configure('TNotebook.Tab',
                       background='#1a1a2e',
                       foreground='#b8b8b8',
                       padding=[20, 8],
                       font=('Arial', 10, 'bold'))
        style.map('TNotebook.Tab',
                 background=[('selected', '#2a2a3e'), ('active', '#252540')],
                 foreground=[('selected', '#ffd700'), ('active', '#ffffff')])

        # Configurar LabelFrame oscuro
        style.configure('TLabelframe',
                       background='#0f0f1a',
                       foreground='#ffd700',
                       borderwidth=2,
                       relief='solid')
        style.configure('TLabelframe.Label',
                       background='#0f0f1a',
                       foreground='#ffd700',
                       font=('Arial', 11, 'bold'))

        # Configurar Entry oscuro
        style.configure('TEntry',
                       fieldbackground='#1a1a2e',
                       background='#1a1a2e',
                       foreground='#ffffff',
                       borderwidth=1,
                       insertcolor='#ffd700')
        style.map('TEntry',
                 focuscolor=[('!focus', '#3a3a4e'), ('focus', '#ffd700')])

        # Configurar Spinbox oscuro
        style.configure('TSpinbox',
                       fieldbackground='#1a1a2e',
                       background='#1a1a2e',
                       foreground='#ffffff',
                       borderwidth=1,
                       arrowcolor='#ffd700')

        # Configurar Combobox oscuro
        style.configure('TCombobox',
                       fieldbackground='#1a1a2e',
                       background='#1a1a2e',
                       foreground='#ffffff',
                       borderwidth=1,
                       arrowcolor='#ffd700')

        # Configurar Checkbutton oscuro
        style.configure('TCheckbutton',
                       background='#0f0f1a',
                       foreground='#e8e8e8',
                       focuscolor='#ffd700')
        style.map('TCheckbutton',
                 background=[('active', '#1a1a2e')],
                 foreground=[('active', '#ffffff')])

        # Configurar Button oscuro
        style.configure('TButton',
                       background='#2a2a3e',
                       foreground='#ffffff',
                       borderwidth=1,
                       font=('Arial', 9, 'bold'))
        style.map('TButton',
                 background=[('active', '#3a3a4e'), ('pressed', '#1a1a2e')],
                 foreground=[('active', '#ffd700')])

        # Configurar Treeview oscuro
        style.configure('Treeview',
                       background='#1a1a2e',
                       foreground='#ffffff',
                       fieldbackground='#1a1a2e',
                       borderwidth=0)
        style.configure('Treeview.Heading',
                       background='#2a2a3e',
                       foreground='#ffd700',
                       font=('Arial', 10, 'bold'))
        style.map('Treeview',
                 background=[('selected', '#3a3a4e')],
                 foreground=[('selected', '#ffd700')])

        # Configurar Scrollbar oscuro
        style.configure('Vertical.TScrollbar',
                       background='#2a2a3e',
                       troughcolor='#1a1a2e',
                       borderwidth=0,
                       arrowcolor='#ffd700')
        style.map('Vertical.TScrollbar',
                 background=[('active', '#3a3a4e')])

        # Configurar Label normal oscuro
        style.configure('TLabel',
                       background='#0f0f1a',
                       foreground='#e8e8e8')

        # Configurar colores adicionales para mejor contraste
        style.configure('Success.TLabel',
                       background='#0f0f1a',
                       foreground='#00ff88',
                       font=('Arial', 10, 'bold'))

        style.configure('Warning.TLabel',
                       background='#0f0f1a',
                       foreground='#ffaa00',
                       font=('Arial', 10, 'bold'))

        style.configure('Error.TLabel',
                       background='#0f0f1a',
                       foreground='#ff6b6b',
                       font=('Arial', 10, 'bold'))
        
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Custom.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Título
        title_label = ttk.Label(main_frame, 
                               text="🌟 Paimon Bot - Gestor de Canjes", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Notebook para pestañas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Pestaña 1: Configuración General
        self.setup_general_tab()
        
        # Pestaña 2: Gestión de Canjes
        self.setup_exchanges_tab()
        
        # Pestaña 3: Configuración Avanzada
        self.setup_advanced_tab()
        
        # Frame de botones
        button_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botones principales
        ttk.Button(button_frame, text="💾 Guardar Configuración", 
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="📁 Cargar Configuración", 
                  command=self.load_config_file).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🔄 Resetear", 
                  command=self.reset_config).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="❌ Salir", 
                  command=self.root.quit).pack(side=tk.RIGHT)
        
    def setup_general_tab(self):
        """Configura la pestaña de configuración general"""
        general_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(general_frame, text="⚙️ General")
        
        # Configuración básica
        basic_frame = ttk.LabelFrame(general_frame, text="Configuración Básica", padding=15)
        basic_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Nombre del bot
        ttk.Label(basic_frame, text="Nombre del Bot:", style='Header.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
        self.bot_name_var = tk.StringVar(value=self.config["bot_name"])
        ttk.Entry(basic_frame, textvariable=self.bot_name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Nombre de la moneda
        ttk.Label(basic_frame, text="Nombre de la Moneda:", style='Header.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)
        self.currency_name_var = tk.StringVar(value=self.config["currency_name"])
        ttk.Entry(basic_frame, textvariable=self.currency_name_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Símbolo de la moneda
        ttk.Label(basic_frame, text="Símbolo de la Moneda:", style='Header.TLabel').grid(row=2, column=0, sticky=tk.W, pady=5)
        self.currency_symbol_var = tk.StringVar(value=self.config["currency_symbol"])
        ttk.Entry(basic_frame, textvariable=self.currency_symbol_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Vista previa
        preview_frame = ttk.LabelFrame(general_frame, text="Vista Previa", padding=15)
        preview_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.preview_label = tk.Label(preview_frame, text="", font=('Arial', 12),
                                     bg='#0f0f1a', fg='#ffd700',
                                     pady=10)
        self.preview_label.pack()
        
        self.update_preview()
        
        # Bind para actualizar vista previa
        self.bot_name_var.trace('w', lambda *args: self.update_preview())
        self.currency_name_var.trace('w', lambda *args: self.update_preview())
        self.currency_symbol_var.trace('w', lambda *args: self.update_preview())
        
    def setup_exchanges_tab(self):
        """Configura la pestaña de gestión de canjes"""
        exchanges_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(exchanges_frame, text="💰 Canjes")
        
        # Frame superior para controles
        controls_frame = ttk.Frame(exchanges_frame, style='Custom.TFrame')
        controls_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(controls_frame, text="➕ Nuevo Canje", 
                  command=self.add_exchange).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(controls_frame, text="✏️ Editar Canje", 
                  command=self.edit_exchange).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(controls_frame, text="🗑️ Eliminar Canje", 
                  command=self.delete_exchange).pack(side=tk.LEFT, padx=(0, 10))
        
        # Lista de canjes
        list_frame = ttk.LabelFrame(exchanges_frame, text="Lista de Canjes", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview para mostrar canjes
        columns = ('ID', 'Nombre', 'Costo', 'Descripción', 'Activo')
        self.exchanges_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configurar columnas
        self.exchanges_tree.heading('ID', text='ID')
        self.exchanges_tree.heading('Nombre', text='Nombre')
        self.exchanges_tree.heading('Costo', text='Costo')
        self.exchanges_tree.heading('Descripción', text='Descripción')
        self.exchanges_tree.heading('Activo', text='Activo')
        
        self.exchanges_tree.column('ID', width=80)
        self.exchanges_tree.column('Nombre', width=150)
        self.exchanges_tree.column('Costo', width=80)
        self.exchanges_tree.column('Descripción', width=250)
        self.exchanges_tree.column('Activo', width=60)
        
        # Scrollbar para la lista
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.exchanges_tree.yview)
        self.exchanges_tree.configure(yscrollcommand=scrollbar.set)
        
        self.exchanges_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.refresh_exchanges_list()
        
    def setup_advanced_tab(self):
        """Configura la pestaña de configuración avanzada"""
        advanced_frame = ttk.Frame(self.notebook, style='Custom.TFrame')
        self.notebook.add(advanced_frame, text="🔧 Avanzado")
        
        # Configuración de límites
        limits_frame = ttk.LabelFrame(advanced_frame, text="Límites de Canjes", padding=15)
        limits_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(limits_frame, text="Costo Mínimo:", style='Header.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
        self.min_cost_var = tk.IntVar(value=self.config["settings"]["min_exchange_cost"])
        ttk.Spinbox(limits_frame, from_=1, to=1000, textvariable=self.min_cost_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(limits_frame, text="Costo Máximo:", style='Header.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)
        self.max_cost_var = tk.IntVar(value=self.config["settings"]["max_exchange_cost"])
        ttk.Spinbox(limits_frame, from_=100, to=100000, textvariable=self.max_cost_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Opciones adicionales
        options_frame = ttk.LabelFrame(advanced_frame, text="Opciones", padding=15)
        options_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.allow_custom_var = tk.BooleanVar(value=self.config["settings"]["allow_custom_exchanges"])
        ttk.Checkbutton(options_frame, text="Permitir canjes personalizados", 
                       variable=self.allow_custom_var).pack(anchor=tk.W, pady=5)
        
        self.auto_save_var = tk.BooleanVar(value=self.config["settings"]["auto_save"])
        ttk.Checkbutton(options_frame, text="Guardado automático", 
                       variable=self.auto_save_var).pack(anchor=tk.W, pady=5)
        
        self.backup_var = tk.BooleanVar(value=self.config["settings"]["backup_enabled"])
        ttk.Checkbutton(options_frame, text="Crear respaldos automáticos", 
                       variable=self.backup_var).pack(anchor=tk.W, pady=5)
        
        # Información del archivo
        info_frame = ttk.LabelFrame(advanced_frame, text="Información del Archivo", padding=15)
        info_frame.pack(fill=tk.X)
        
        ttk.Label(info_frame, text=f"Archivo: {self.config_file}").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text=f"Creado: {self.config.get('created_date', 'N/A')}").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text=f"Modificado: {self.config.get('last_modified', 'N/A')}").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text=f"Versión: {self.config.get('version', '1.0')}").pack(anchor=tk.W, pady=2)

    def update_preview(self):
        """Actualiza la vista previa de la configuración"""
        bot_name = self.bot_name_var.get() or "Paimon"
        currency_name = self.currency_name_var.get() or "Primogemas"
        currency_symbol = self.currency_symbol_var.get() or "💎"

        preview_text = f"Bot: {bot_name} | Moneda: {currency_symbol} {currency_name}"
        self.preview_label.config(text=preview_text)

    def add_exchange(self):
        """Abre ventana para agregar nuevo canje"""
        self.exchange_dialog()

    def edit_exchange(self):
        """Abre ventana para editar canje seleccionado"""
        selection = self.exchanges_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un canje para editar")
            return

        item = self.exchanges_tree.item(selection[0])
        exchange_id = item['values'][0]

        if exchange_id in self.config["exchanges"]:
            self.exchange_dialog(exchange_id)

    def delete_exchange(self):
        """Elimina el canje seleccionado"""
        selection = self.exchanges_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Selecciona un canje para eliminar")
            return

        item = self.exchanges_tree.item(selection[0])
        exchange_id = item['values'][0]
        exchange_name = item['values'][1]

        if messagebox.askyesno("Confirmar", f"¿Eliminar el canje '{exchange_name}'?"):
            if exchange_id in self.config["exchanges"]:
                del self.config["exchanges"][exchange_id]
                self.refresh_exchanges_list()
                messagebox.showinfo("Éxito", "Canje eliminado correctamente")

    def exchange_dialog(self, exchange_id=None):
        """Ventana de diálogo para crear/editar canjes"""
        dialog = tk.Toplevel(self.root)
        dialog.title("➕ Nuevo Canje" if exchange_id is None else "✏️ Editar Canje")
        dialog.geometry("500x400")
        dialog.configure(bg='#0f0f1a')
        dialog.transient(self.root)
        dialog.grab_set()

        # Centrar ventana
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Variables
        name_var = tk.StringVar()
        cost_var = tk.IntVar(value=100)
        description_var = tk.StringVar()
        active_var = tk.BooleanVar(value=True)
        category_var = tk.StringVar(value="General")
        cooldown_var = tk.IntVar(value=0)
        max_uses_var = tk.IntVar(value=0)

        # Si estamos editando, cargar datos existentes
        if exchange_id and exchange_id in self.config["exchanges"]:
            exchange = self.config["exchanges"][exchange_id]
            name_var.set(exchange["name"])
            cost_var.set(exchange["cost"])
            description_var.set(exchange["description"])
            active_var.set(exchange["active"])
            category_var.set(exchange.get("category", "General"))
            cooldown_var.set(exchange.get("cooldown", 0))
            max_uses_var.set(exchange.get("max_uses", 0))

        # Frame principal
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Información básica
        basic_frame = ttk.LabelFrame(main_frame, text="Información Básica", padding=15)
        basic_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(basic_frame, text="Nombre del Canje:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(basic_frame, textvariable=name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Costo:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(basic_frame, from_=1, to=100000, textvariable=cost_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Categoría:").grid(row=2, column=0, sticky=tk.W, pady=5)
        category_combo = ttk.Combobox(basic_frame, textvariable=category_var, width=20)
        category_combo['values'] = ('General', 'Destacados', 'Especiales', 'Temporales', 'VIP')
        category_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(basic_frame, text="Descripción:").grid(row=3, column=0, sticky=tk.NW, pady=5)
        desc_text = tk.Text(basic_frame, width=40, height=3,
                           bg='#1a1a2e', fg='#ffffff',
                           insertbackground='#ffd700',
                           selectbackground='#3a3a4e',
                           selectforeground='#ffffff',
                           borderwidth=1, relief='solid')
        desc_text.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        desc_text.insert('1.0', description_var.get())

        # Configuración avanzada
        advanced_frame = ttk.LabelFrame(main_frame, text="Configuración Avanzada", padding=15)
        advanced_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Checkbutton(advanced_frame, text="Canje activo", variable=active_var).grid(row=0, column=0, sticky=tk.W, pady=5)

        ttk.Label(advanced_frame, text="Tiempo de espera (segundos):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(advanced_frame, from_=0, to=3600, textvariable=cooldown_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(advanced_frame, text="Usos máximos (0 = ilimitado):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(advanced_frame, from_=0, to=1000, textvariable=max_uses_var, width=15).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        def save_exchange():
            name = name_var.get().strip()
            cost = cost_var.get()
            description = desc_text.get('1.0', tk.END).strip()

            if not name:
                messagebox.showerror("Error", "El nombre del canje es obligatorio")
                return

            if cost < self.config["settings"]["min_exchange_cost"] or cost > self.config["settings"]["max_exchange_cost"]:
                messagebox.showerror("Error", f"El costo debe estar entre {self.config['settings']['min_exchange_cost']} y {self.config['settings']['max_exchange_cost']}")
                return

            # Crear o actualizar canje
            if exchange_id is None:
                new_id = str(uuid.uuid4())[:8]
            else:
                new_id = exchange_id

            self.config["exchanges"][new_id] = {
                "name": name,
                "cost": cost,
                "description": description,
                "active": active_var.get(),
                "category": category_var.get(),
                "cooldown": cooldown_var.get(),
                "max_uses": max_uses_var.get(),
                "created_date": datetime.now().isoformat() if exchange_id is None else self.config["exchanges"][exchange_id].get("created_date"),
                "modified_date": datetime.now().isoformat(),
                "uses_count": 0 if exchange_id is None else self.config["exchanges"][exchange_id].get("uses_count", 0)
            }

            self.refresh_exchanges_list()
            dialog.destroy()
            messagebox.showinfo("Éxito", "Canje guardado correctamente")

        ttk.Button(button_frame, text="💾 Guardar", command=save_exchange).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancelar", command=dialog.destroy).pack(side=tk.LEFT)

    def refresh_exchanges_list(self):
        """Actualiza la lista de canjes en el TreeView"""
        # Limpiar lista actual
        for item in self.exchanges_tree.get_children():
            self.exchanges_tree.delete(item)

        # Agregar canjes
        for exchange_id, exchange in self.config["exchanges"].items():
            self.exchanges_tree.insert('', tk.END, values=(
                exchange_id,
                exchange["name"],
                f"{exchange['cost']} {self.config['currency_symbol']}",
                exchange["description"][:50] + "..." if len(exchange["description"]) > 50 else exchange["description"],
                "✅" if exchange["active"] else "❌"
            ))

    def load_config(self):
        """Carga la configuración desde el archivo .Paimon"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Fusionar con configuración por defecto
                    self.config.update(loaded_config)
                print(f"✅ Configuración cargada desde {self.config_file}")
            else:
                print(f"📄 Archivo {self.config_file} no existe, usando configuración por defecto")
                self.save_config()  # Crear archivo con configuración por defecto
        except Exception as e:
            messagebox.showerror("Error", f"Error al cargar configuración: {e}")
            print(f"❌ Error cargando configuración: {e}")

    def save_config(self):
        """Guarda la configuración actual al archivo .Paimon"""
        try:
            # Actualizar configuración con valores actuales de la UI
            self.update_config_from_ui()

            # Crear respaldo si está habilitado
            if self.config["settings"]["backup_enabled"] and os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                import shutil
                shutil.copy2(self.config_file, backup_file)
                print(f"🔄 Respaldo creado: {backup_file}")

            # Guardar configuración
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("Éxito", f"Configuración guardada en {self.config_file}")
            print(f"💾 Configuración guardada en {self.config_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Error al guardar configuración: {e}")
            print(f"❌ Error guardando configuración: {e}")

    def update_config_from_ui(self):
        """Actualiza la configuración con los valores actuales de la interfaz"""
        try:
            self.config["bot_name"] = self.bot_name_var.get()
            self.config["currency_name"] = self.currency_name_var.get()
            self.config["currency_symbol"] = self.currency_symbol_var.get()

            self.config["settings"]["min_exchange_cost"] = self.min_cost_var.get()
            self.config["settings"]["max_exchange_cost"] = self.max_cost_var.get()
            self.config["settings"]["allow_custom_exchanges"] = self.allow_custom_var.get()
            self.config["settings"]["auto_save"] = self.auto_save_var.get()
            self.config["settings"]["backup_enabled"] = self.backup_var.get()

            self.config["last_modified"] = datetime.now().isoformat()
        except Exception as e:
            print(f"⚠️ Error actualizando configuración desde UI: {e}")

    def load_config_file(self):
        """Carga configuración desde un archivo seleccionado"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo de configuración",
            filetypes=[("Archivos Paimon", "*.Paimon"), ("Archivos JSON", "*.json"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)

                self.config.update(loaded_config)
                self.refresh_ui()
                messagebox.showinfo("Éxito", f"Configuración cargada desde {file_path}")

            except Exception as e:
                messagebox.showerror("Error", f"Error al cargar archivo: {e}")

    def refresh_ui(self):
        """Actualiza la interfaz con los valores de configuración actuales"""
        try:
            self.bot_name_var.set(self.config["bot_name"])
            self.currency_name_var.set(self.config["currency_name"])
            self.currency_symbol_var.set(self.config["currency_symbol"])

            self.min_cost_var.set(self.config["settings"]["min_exchange_cost"])
            self.max_cost_var.set(self.config["settings"]["max_exchange_cost"])
            self.allow_custom_var.set(self.config["settings"]["allow_custom_exchanges"])
            self.auto_save_var.set(self.config["settings"]["auto_save"])
            self.backup_var.set(self.config["settings"]["backup_enabled"])

            self.refresh_exchanges_list()
            self.update_preview()

        except Exception as e:
            print(f"⚠️ Error actualizando interfaz: {e}")

    def reset_config(self):
        """Resetea la configuración a valores por defecto"""
        if messagebox.askyesno("Confirmar Reset", "¿Estás seguro de que quieres resetear toda la configuración?"):
            self.config = {
                "bot_name": "Paimon",
                "currency_name": "Primogemas",
                "currency_symbol": "💎",
                "exchanges": {},
                "settings": {
                    "min_exchange_cost": 10,
                    "max_exchange_cost": 10000,
                    "allow_custom_exchanges": True,
                    "auto_save": True,
                    "backup_enabled": True
                },
                "created_date": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
                "version": "1.0"
            }

            self.refresh_ui()
            messagebox.showinfo("Reset Completo", "Configuración reseteada a valores por defecto")

    def run(self):
        """Ejecuta la aplicación"""
        # Configurar protocolo de cierre
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Centrar ventana
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")

        print("🌟 Paimon Config Manager iniciado")
        self.root.mainloop()

    def on_closing(self):
        """Maneja el cierre de la aplicación"""
        if self.config["settings"]["auto_save"]:
            self.save_config()

        if messagebox.askokcancel("Salir", "¿Quieres cerrar el Gestor de Configuración?"):
            print("👋 Cerrando Paimon Config Manager")
            self.root.destroy()

def main():
    """Función principal"""
    print("🌟 Iniciando Paimon Bot - Gestor de Configuración de Canjes")
    print("=" * 60)

    try:
        app = PaimonConfigManager()
        app.run()
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        messagebox.showerror("Error Crítico", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()
