# 📁 Archivos OBS SÚPER DECORADOS - Guía de Configuración

Este directorio contiene archivos que se actualizan automáticamente para mostrar información en tiempo real en tu stream de OBS. ¡Todos están súper decorados con emojis, ASCII art y diseños llamativos!

## 📄 Archivos Disponibles

### 1. `active_viewers.txt` 🔥
**Espectadores Más Activos - HALL OF FAME**
- Top 10 usuarios más activos con decoración de medallas
- Estados visuales: 🟢 ✨ ACTIVO AHORA ✨, 🟡 💫 Reciente 💫, 🔴 💤 Inactivo 💤
- Barras de progreso ASCII para cada usuario
- Marcos decorativos según posición (👑 Rey, 🥈 Noble, 🥉 Héroe)
- Bordes ASCII súper llamativos

### 2. `top_contributors.txt` ⭐
**Top Contribuidores - LEGENDS**
- Top 8 usuarios con títulos especiales según puntos
- Medallas y marcos: 👑 REY/REINA, 🥈 NOBLE, 🥉 HÉROE, 🌟 ESTRELLA
- Barras de progreso para puntos y niveles
- Títulos dinámicos: 🔥 LEYENDA ABSOLUTA, ⚡ MAESTRO DEL CHAT, etc.
- Decoración con diamantes y marcos ASCII

### 3. `stream_stats.txt` 📊
**Estadísticas LIVE - STREAM STATUS**
- Tiempo activo con mensajes motivacionales
- Barras de progreso visuales para todas las estadísticas
- Estados dinámicos: 🔥 ¡CHAT EN LLAMAS!, ⚡ ¡MUY ACTIVO!, etc.
- Secciones separadas con marcos decorativos
- Información de sorteos con barras de participación

### 4. `current_level.txt` 🎯
**Nivel Actual - NOW PLAYING**
- Información completa del nivel que se está jugando
- Barras de popularidad basadas en likes
- Mensajes motivacionales según dificultad
- Decoración especial para niveles demon
- Marcos ASCII llamativos

### 5. `next_levels.txt` 🎮
**Próximos Niveles - QUEUE LIST**
- Top 8 niveles en la cola con posiciones
- Iconos especiales: 👑 SIGUIENTE, 🥈 PREPARADO, 🥉 EN ESPERA
- Barras de posición visual
- Estados dinámicos para cada nivel
- Contador de niveles restantes

## 🎥 Cómo Configurar en OBS

### Paso 1: Agregar Fuente de Texto
1. En OBS, haz clic en **"+"** en la sección de Fuentes
2. Selecciona **"Texto (GDI+)"**
3. Dale un nombre (ej: "Espectadores Activos")

### Paso 2: Configurar la Fuente
1. Marca la casilla **"Leer desde archivo"**
2. Haz clic en **"Examinar"** y selecciona uno de los archivos:
   - `active_viewers.txt` para espectadores activos
   - `top_contributors.txt` para contribuidores
   - `stream_stats.txt` para estadísticas

### Paso 3: Personalizar Apariencia
- **Fuente**: Recomendado "Consolas" o "Courier New" (monoespaciada)
- **Tamaño**: 14-18 para buena legibilidad
- **Color**: Blanco o amarillo para contraste
- **Fondo**: Opcional, color sólido con transparencia

### Paso 4: Posicionamiento
- **Esquina superior derecha**: Para estadísticas del stream
- **Lateral izquierdo**: Para lista de espectadores activos
- **Parte inferior**: Para top contribuidores

## 🔄 Actualización Automática

Los archivos se actualizan automáticamente:
- **Cada 10 mensajes** en el chat
- **Cuando alguien envía un nivel** (actualiza puntos)
- **Durante sorteos** (actualiza participantes)
- **Manualmente** con el comando `!obsupdate`

## 🎨 Consejos de Diseño

### Para mejor visualización:
- Usa **fondos semitransparentes** para no obstruir el juego
- **Colores contrastantes** (texto blanco sobre fondo oscuro)
- **Tamaño de fuente consistente** en todas las fuentes
- **Posicionamiento estratégico** que no tape elementos importantes

### Ejemplos de configuración:
```
Espectadores Activos:
- Posición: Esquina superior izquierda
- Tamaño: 16px
- Color: #FFFFFF (blanco)
- Fondo: #000000 con 70% transparencia

Top Contribuidores:
- Posición: Lateral derecho
- Tamaño: 14px
- Color: #FFD700 (dorado)
- Fondo: #1a1a1a con 80% transparencia

Estadísticas:
- Posición: Parte superior central
- Tamaño: 18px
- Color: #00FF00 (verde)
- Fondo: Sin fondo
```

## 🎮 Comandos del Bot Relacionados

- `!topactive` - Muestra top 5 activos en chat
- `!obsinfo` - Información sobre archivos OBS
- `!obsupdate` - Actualizar archivos manualmente (solo mods)

## 🔧 Solución de Problemas

### Si los archivos no se actualizan:
1. Verifica que el bot tenga permisos de escritura en la carpeta
2. Usa `!obsupdate` para forzar actualización
3. Reinicia OBS si no detecta cambios

### Si el texto se ve mal:
1. Usa fuentes monoespaciadas (Consolas, Courier New)
2. Ajusta el tamaño de fuente según resolución
3. Verifica la codificación UTF-8 en OBS

### Si hay caracteres extraños:
1. En OBS, ve a Propiedades de la fuente de texto
2. Cambia la codificación a "UTF-8"
3. Reinicia la fuente si es necesario

## 📊 Personalización Avanzada

Puedes modificar los archivos manualmente si necesitas un formato específico, pero se sobrescribirán con la próxima actualización automática.

Para cambios permanentes, contacta al desarrollador del bot.

---

**¡Disfruta mostrando tu comunidad activa en el stream! 🎉**
