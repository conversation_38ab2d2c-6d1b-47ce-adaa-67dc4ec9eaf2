#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ejemplo de comandos para integrar el sistema de canjes con el bot Paimon
Estos comandos se pueden agregar al archivo PBot.py
"""

from paimon_exchange_integration import PaimonExchangeSystem

class BotExchangeCommands:
    def __init__(self):
        """Inicializa el sistema de canjes"""
        self.exchange_system = PaimonExchangeSystem()
        
    # ==================== COMANDOS DE CANJES ====================
    
    async def cmd_tienda(self, ctx):
        """Muestra la tienda de canjes disponibles"""
        try:
            # Obtener categoría si se especifica
            parts = ctx.message.content.split()
            category = parts[1] if len(parts) > 1 else None
            
            # Generar display de la tienda
            shop_lines = self.exchange_system.get_shop_display(category)
            
            # Enviar en múltiples mensajes si es necesario
            current_message = ""
            for line in shop_lines:
                if len(current_message + line + "\n") > 450:  # Límite de Twitch
                    await ctx.send(current_message)
                    current_message = line + "\n"
                else:
                    current_message += line + "\n"
            
            if current_message:
                await ctx.send(current_message)
                
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error mostrando tienda: {e}")
    
    async def cmd_canje(self, ctx):
        """Permite a un usuario realizar un canje"""
        try:
            parts = ctx.message.content.split(maxsplit=1)
            if len(parts) < 2:
                await ctx.send(f"@{ctx.author.name} uso: !canje <nombre_del_canje>")
                return
            
            exchange_name = parts[1]
            user_id = ctx.author.name.lower()
            channel = ctx.channel.name.lower()
            
            # Buscar el canje por nombre
            exchange_result = self.exchange_system.find_exchange_by_name(exchange_name)
            if not exchange_result:
                await ctx.send(f"@{ctx.author.name} canje '{exchange_name}' no encontrado. Usa !tienda para ver canjes disponibles.")
                return
            
            exchange_id, exchange_data = exchange_result
            
            # Obtener puntos del usuario (esto debe conectarse con tu sistema de puntos)
            user_points = self.get_user_points(channel, user_id)
            
            # Procesar el canje
            success, message, remaining_points = self.exchange_system.process_exchange(
                user_id, exchange_id, user_points
            )
            
            if success:
                # Actualizar puntos del usuario
                self.set_user_points(channel, user_id, remaining_points)
                
                # Aplicar el efecto del canje
                await self.apply_exchange_effect(ctx, exchange_id, exchange_data)
                
            await ctx.send(f"@{ctx.author.name} {message}")
            
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error procesando canje: {e}")
    
    async def cmd_info_canje(self, ctx):
        """Muestra información detallada de un canje"""
        try:
            parts = ctx.message.content.split(maxsplit=1)
            if len(parts) < 2:
                await ctx.send(f"@{ctx.author.name} uso: !info <nombre_del_canje>")
                return
            
            exchange_name = parts[1]
            
            # Buscar el canje
            exchange_result = self.exchange_system.find_exchange_by_name(exchange_name)
            if not exchange_result:
                await ctx.send(f"@{ctx.author.name} canje '{exchange_name}' no encontrado.")
                return
            
            exchange_id, _ = exchange_result
            info = self.exchange_system.get_exchange_info(exchange_id)
            
            if info:
                await ctx.send(f"@{ctx.author.name}\n{info}")
            else:
                await ctx.send(f"@{ctx.author.name} no se pudo obtener información del canje.")
                
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error obteniendo información: {e}")
    
    async def cmd_categorias(self, ctx):
        """Muestra las categorías disponibles de canjes"""
        try:
            active_exchanges = self.exchange_system.get_active_exchanges()
            categories = set()
            
            for exchange in active_exchanges.values():
                categories.add(exchange.get('category', 'General'))
            
            if categories:
                category_list = ", ".join(sorted(categories))
                await ctx.send(f"📂 Categorías disponibles: {category_list}")
                await ctx.send("Usa !tienda <categoría> para ver canjes específicos")
            else:
                await ctx.send("No hay categorías disponibles")
                
        except Exception as e:
            await ctx.send(f"@{ctx.author.name} error obteniendo categorías: {e}")
    
    async def cmd_mis_canjes(self, ctx):
        """Muestra el historial de canjes del usuario (si se implementa tracking)"""
        # Esta función requeriría un sistema de tracking de canjes por usuario
        await ctx.send(f"@{ctx.author.name} función de historial en desarrollo 🚧")
    
    # ==================== EFECTOS DE CANJES ====================
    
    async def apply_exchange_effect(self, ctx, exchange_id: str, exchange_data: dict):
        """Aplica el efecto específico de un canje"""
        exchange_name = exchange_data['name'].lower()
        
        # Mapear nombres de canjes a efectos
        if "destacar" in exchange_name or "highlight" in exchange_name:
            await self.effect_highlight_message(ctx)
        elif "saltarse" in exchange_name or "skip" in exchange_name:
            await self.effect_skip_queue(ctx)
        elif "elegir" in exchange_name or "choose" in exchange_name:
            await self.effect_choose_level(ctx)
        elif "aleatorio" in exchange_name or "random" in exchange_name:
            await self.effect_random_level(ctx)
        elif "mensaje" in exchange_name or "message" in exchange_name:
            await self.effect_custom_message(ctx)
        elif "sorteo" in exchange_name or "raffle" in exchange_name:
            await self.effect_free_raffle(ctx)
        elif "bonus" in exchange_name or "puntos" in exchange_name:
            await self.effect_bonus_points(ctx)
        else:
            # Efecto genérico
            await ctx.send(f"✨ Efecto de '{exchange_data['name']}' aplicado!")
    
    async def effect_highlight_message(self, ctx):
        """Efecto: Destacar próximo mensaje"""
        user_id = ctx.author.name.lower()
        channel = ctx.channel.name.lower()
        
        # Marcar al usuario para destacar su próximo mensaje
        # (esto requiere modificar el sistema de mensajes del bot)
        self.set_user_highlight_next(channel, user_id, True)
        await ctx.send("✨ Tu próximo mensaje será destacado!")
    
    async def effect_skip_queue(self, ctx):
        """Efecto: Saltarse la cola"""
        user_id = ctx.author.name.lower()
        channel = ctx.channel.name.lower()
        
        # Marcar al usuario para que su próximo nivel vaya al frente
        self.set_user_skip_queue(channel, user_id, True)
        await ctx.send("🚀 Tu próximo nivel irá al frente de la cola!")
    
    async def effect_choose_level(self, ctx):
        """Efecto: Elegir próximo nivel"""
        await ctx.send("👑 Puedes elegir el próximo nivel! Usa !elegir <posición> en los próximos 5 minutos")
    
    async def effect_random_level(self, ctx):
        """Efecto: Nivel aleatorio"""
        # Implementar lógica de nivel aleatorio
        await ctx.send("🎲 Se ha seleccionado un nivel aleatorio como próximo!")
    
    async def effect_custom_message(self, ctx):
        """Efecto: Mensaje personalizado"""
        await ctx.send("💬 Puedes enviar un mensaje personalizado! Usa !mensaje <texto> en los próximos 5 minutos")
    
    async def effect_free_raffle(self, ctx):
        """Efecto: Entrada gratis a sorteo"""
        user_id = ctx.author.name.lower()
        channel = ctx.channel.name.lower()
        
        # Agregar entrada gratuita al próximo sorteo
        self.add_free_raffle_entry(channel, user_id)
        await ctx.send("🎉 Has obtenido una entrada gratuita para el próximo sorteo!")
    
    async def effect_bonus_points(self, ctx):
        """Efecto: Puntos bonus"""
        import random
        
        user_id = ctx.author.name.lower()
        channel = ctx.channel.name.lower()
        
        bonus = random.randint(50, 150)
        current_points = self.get_user_points(channel, user_id)
        new_points = current_points + bonus
        
        self.set_user_points(channel, user_id, new_points)
        
        currency_symbol = self.exchange_system.config.get('currency_symbol', '💎')
        await ctx.send(f"💰 Has ganado {bonus} {currency_symbol} bonus! Total: {new_points}")
    
    # ==================== FUNCIONES DE INTEGRACIÓN ====================
    # Estas funciones deben conectarse con tu sistema existente de puntos
    
    def get_user_points(self, channel: str, user_id: str) -> int:
        """Obtiene los puntos de un usuario (conectar con tu sistema)"""
        # Ejemplo de integración con el sistema existente
        # return user_data[channel][user_id]["points"]
        return 500  # Valor de ejemplo
    
    def set_user_points(self, channel: str, user_id: str, points: int):
        """Establece los puntos de un usuario (conectar con tu sistema)"""
        # Ejemplo de integración
        # user_data[channel][user_id]["points"] = points
        pass
    
    def set_user_highlight_next(self, channel: str, user_id: str, highlight: bool):
        """Marca al usuario para destacar su próximo mensaje"""
        # Implementar en tu sistema de mensajes
        pass
    
    def set_user_skip_queue(self, channel: str, user_id: str, skip: bool):
        """Marca al usuario para saltarse la cola"""
        # Implementar en tu sistema de cola
        pass
    
    def add_free_raffle_entry(self, channel: str, user_id: str):
        """Agrega entrada gratuita a sorteo"""
        # Implementar en tu sistema de sorteos
        pass

# ==================== COMANDOS PARA AGREGAR AL BOT ====================

"""
Para integrar estos comandos en tu bot PBot.py, agrega estas líneas:

1. Al inicio del archivo, después de los imports:
from bot_commands_example import BotExchangeCommands

2. En la clase Bot, en __init__:
self.exchange_commands = BotExchangeCommands()

3. Agregar los comandos:

@commands.command(name='tienda')
async def tienda(self, ctx):
    await self.exchange_commands.cmd_tienda(ctx)

@commands.command(name='canje')
async def canje(self, ctx):
    await self.exchange_commands.cmd_canje(ctx)

@commands.command(name='info')
async def info_canje(self, ctx):
    await self.exchange_commands.cmd_info_canje(ctx)

@commands.command(name='categorias')
async def categorias(self, ctx):
    await self.exchange_commands.cmd_categorias(ctx)
"""
