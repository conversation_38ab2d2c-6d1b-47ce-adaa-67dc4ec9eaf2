# 🌟 Paimon Bot - Gestor de Configuración de Canjes

## 📋 Descripción

Esta es una **interfaz gráfica de Python** para gestionar los canjes de monedas del bot Paimon. Permite crear, editar y configurar canjes personalizados que los usuarios pueden comprar con las monedas del bot.

## ✨ Características Principales

### 🎨 Interfaz Gráfica Intuitiva
- **Tema oscuro completo** estilo Genshin Impact con tonos profundos
- **Colores elegantes**: Fondo negro profundo (#0f0f1a) con acentos dorados
- **3 pestañas organizadas**: General, Canjes y Avanzado
- **Controles fáciles de usar** con validación automática
- **Vista previa en tiempo real** de la configuración

### 💰 Gestión Completa de Canjes
- **Crear canjes personalizados** con nombre, costo y descripción
- **Categorías organizadas**: General, Destacados, Especiales, Temporales, VIP
- **Configuración avanzada**: Tiempo de espera, usos máximos, estado activo/inactivo
- **Lista visual** con todos los canjes y su información

### 📁 Archivo de Configuración .Paimon
- **Formato JSON** fácil de leer y editar
- **Respaldos automáticos** antes de cada guardado
- **Versionado** para compatibilidad futura
- **Metadatos** con fechas de creación y modificación

## 🚀 Instalación y Uso

### Requisitos
```bash
# Python 3.7 o superior (tkinter incluido por defecto)
# No requiere instalaciones adicionales
```

### Ejecutar el Gestor
```bash
python paimon_config_manager.py
```

## 🎮 Cómo Usar la Interfaz

### Pestaña ⚙️ General
1. **Configura el nombre del bot** (ej: "Paimon")
2. **Define la moneda** (ej: "Primogemas" con símbolo "💎")
3. **Ve la vista previa** actualizada en tiempo real

### Pestaña 💰 Canjes
1. **➕ Nuevo Canje**: Crea un canje desde cero
2. **✏️ Editar Canje**: Modifica un canje existente
3. **🗑️ Eliminar Canje**: Borra un canje seleccionado
4. **Lista visual**: Ve todos los canjes con su información

### Pestaña 🔧 Avanzado
1. **Configura límites** de costo mínimo y máximo
2. **Activa opciones** como canjes personalizados y respaldos
3. **Ve información** del archivo de configuración

## 💎 Crear un Canje

### Información Básica
- **Nombre**: Título del canje (ej: "Destacar Mensaje")
- **Costo**: Precio en monedas del bot (ej: 150)
- **Categoría**: Tipo de canje (General, Destacados, etc.)
- **Descripción**: Explicación de qué hace el canje

### Configuración Avanzada
- **Activo**: Si el canje está disponible o no
- **Tiempo de espera**: Segundos antes de poder usar de nuevo
- **Usos máximos**: Límite de veces que se puede usar (0 = ilimitado)

## 📁 Archivo .Paimon

### Estructura del Archivo
```json
{
  "bot_name": "Paimon",
  "currency_name": "Primogemas",
  "currency_symbol": "💎",
  "exchanges": {
    "12345678": {
      "name": "Destacar Mensaje",
      "cost": 150,
      "description": "Tu próximo mensaje será destacado",
      "active": true,
      "category": "Destacados",
      "cooldown": 300,
      "max_uses": 0
    }
  },
  "settings": {
    "min_exchange_cost": 10,
    "max_exchange_cost": 10000,
    "allow_custom_exchanges": true,
    "auto_save": true,
    "backup_enabled": true
  }
}
```

### Ubicación del Archivo
- **Archivo principal**: `.Paimon` (en la misma carpeta del gestor)
- **Respaldos**: `.Paimon.backup.YYYYMMDD_HHMMSS`

## 🎯 Ejemplos de Canjes Predefinidos

### 💬 Canjes de Chat
- **Destacar Mensaje** (150 💎): Mensaje con colores especiales
- **Mensaje Personalizado** (300 💎): Mensaje mostrado en pantalla
- **Emote Personalizado** (250 💎): Emote especial en chat

### 🎮 Canjes de Niveles
- **Saltarse Cola** (500 💎): Nivel va al frente de la cola
- **Elegir Próximo Nivel** (800 💎): Seleccionar próximo nivel
- **Nivel Aleatorio** (200 💎): Nivel random como próximo

### 🎁 Canjes Especiales
- **Entrada Sorteo Gratis** (400 💎): Entrada gratuita a sorteos
- **Puntos Bonus** (100 💎): 50-150 puntos adicionales

## ⚙️ Configuración Avanzada

### Límites de Costo
- **Mínimo**: 10 💎 (evita canjes muy baratos)
- **Máximo**: 10,000 💎 (evita canjes excesivamente caros)

### Opciones Adicionales
- **Canjes personalizados**: Permite a usuarios sugerir canjes
- **Guardado automático**: Guarda al cerrar la aplicación
- **Respaldos**: Crea copias de seguridad automáticamente

## 🔧 Integración con el Bot

### Cómo Usar en el Bot
1. **Guarda la configuración** en el gestor
2. **Copia el archivo .Paimon** a la carpeta del bot
3. **Modifica el bot** para leer los canjes desde el archivo
4. **Implementa los comandos** !canje, !shop, !buy, etc.

### Ejemplo de Integración
```python
import json

# Cargar configuración de canjes
with open('.Paimon', 'r', encoding='utf-8') as f:
    paimon_config = json.load(f)

# Obtener canjes activos
active_exchanges = {
    id: exchange for id, exchange in paimon_config["exchanges"].items()
    if exchange["active"]
}

# Mostrar tienda
def show_shop():
    currency = paimon_config["currency_symbol"]
    for id, exchange in active_exchanges.items():
        print(f"{exchange['name']}: {exchange['cost']} {currency}")
```

## 🛠️ Solución de Problemas

### El gestor no abre
1. Verifica que tengas Python 3.7+ instalado
2. Asegúrate de que tkinter esté disponible
3. Ejecuta desde la terminal para ver errores

### Error al guardar configuración
1. Verifica permisos de escritura en la carpeta
2. Asegúrate de que no haya otro programa usando el archivo
3. Revisa que el disco tenga espacio disponible

### Los canjes no aparecen en el bot
1. Verifica que el archivo .Paimon esté en la carpeta correcta
2. Asegúrate de que el bot esté leyendo el archivo
3. Revisa que los canjes estén marcados como "activos"

## 🎯 Próximas Mejoras

- [ ] **Importar/Exportar** configuraciones
- [ ] **Plantillas** de canjes predefinidas
- [ ] **Estadísticas** de uso de canjes
- [ ] **Validación avanzada** de configuraciones
- [ ] **Integración directa** con el bot
- [ ] **Editor de comandos** personalizados

---

**¡Gestiona los canjes de tu bot Paimon de forma fácil y visual!** 🌟💎

### 💡 Consejo
*Crea canjes variados con diferentes precios para que todos los usuarios puedan participar, desde canjes baratos hasta recompensas premium.*
