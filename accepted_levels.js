const crypto = require('crypto');

// Map to store levels by uniqueId
// Each entry: {
//   uniqueId, levelId, name, author,
//   importanceMap: Map of streamer to importance,
//   streamers: array of streamer names who saved the level
// }
const acceptedLevels = new Map();

/**
 * Generate a unique ID based on levelId, name, and author
 * @param {string} levelId 
 * @param {string} name 
 * @param {string} author 
 * @returns {string} uniqueId
 */
function generateUniqueId(levelId, name, author) {
  const hash = crypto.createHash('md5');
  hash.update(levelId + name.toLowerCase() + author.toLowerCase());
  return hash.digest('hex').slice(0, 16);
}

/**
 * Calculate average importance from importanceMap
 * @param {Map} importanceMap - Map of streamer to importance
 * @returns {number} average importance
 */
function calculateAverageImportance(importanceMap) {
  const values = Array.from(importanceMap.values());
  if (values.length === 0) return 0;
  const total = values.reduce((acc, val) => acc + val, 0);
  return total / values.length;
}

/**
 * Add or update a level for a streamer with importance
 * @param {string} streamer - name of the streamer saving the level
 * @param {object} levelData - { levelId, name, author }
 * @param {number} importance - importance value given by the streamer
 * @returns {string} message indicating result
 */
function addOrUpdateLevel(streamer, levelData, importance) {
  const { levelId, name, author } = levelData;
  const uniqueId = generateUniqueId(levelId, name, author);

  if (!acceptedLevels.has(uniqueId)) {
    // New level entry
    const importanceMap = new Map();
    importanceMap.set(streamer, importance);
    acceptedLevels.set(uniqueId, {
      uniqueId,
      levelId,
      name,
      author,
      importanceMap,
      streamers: [streamer],
      importance: importance
    });
    return `Nivel guardado exitosamente por ${streamer}.`;
  } else {
    // Level exists, update if streamer not already saved or update importance if streamer exists
    const existing = acceptedLevels.get(uniqueId);
    if (existing.importanceMap.has(streamer)) {
      return `El nivel ya fue guardado previamente por ${streamer}.`;
    } else {
      existing.importanceMap.set(streamer, importance);
      existing.streamers.push(streamer);
      existing.importance = calculateAverageImportance(existing.importanceMap);
      acceptedLevels.set(uniqueId, existing);
      return `Nivel actualizado con la importancia promedio. Guardado también por ${streamer}.`;
    }
  }
}

/**
 * Get all accepted levels as an array
 * @returns {Array} levels
 */
function getAllLevels() {
  // Return levels with importance as average calculated from importanceMap
  return Array.from(acceptedLevels.values()).map(level => ({
    uniqueId: level.uniqueId,
    levelId: level.levelId,
    name: level.name,
    author: level.author,
    importance: level.importance,
    streamers: level.streamers
  }));
}

module.exports = {
  addOrUpdateLevel,
  getAllLevels,
  generateUniqueId
};
