<!-- CÓDIGO DE NAVEGACIÓN PARA AGREGAR A TU INDEX.HTML -->
<!-- Agrega este código después del <body> y antes de tu contenido principal -->

<nav style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(15px); padding: 15px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); position: sticky; top: 0; z-index: 1000;">
    <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: center; align-items: center; gap: 30px; padding: 0 20px;">
        <a href="index.html" style="color: #4ecdc4; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid rgba(78, 205, 196, 0.3); background: rgba(78, 205, 196, 0.1);">
            🏠 Inicio
        </a>
        <a href="bot_commands.html" style="color: #b8c6db; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid transparent;">
            📋 Comandos
        </a>
        <a href="https://twitch.tv/flozwer" target="_blank" style="color: #b8c6db; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid transparent;">
            📺 Twitch
        </a>
        <a href="https://github.com/Fl0zWer/Paimon" target="_blank" style="color: #b8c6db; text-decoration: none; padding: 10px 20px; border-radius: 10px; transition: all 0.3s ease; border: 1px solid transparent;">
            💻 GitHub
        </a>
    </div>
</nav>

<!-- CSS ADICIONAL PARA AGREGAR A TU INDEX.HTML -->
<!-- Agrega este código dentro de tu <style> existente -->

<style>
/* Efectos hover para la navegación */
nav a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Animación de gradiente para elementos activos */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Efecto de brillo en elementos importantes */
.highlight-element {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

/* Scrollbar personalizada para consistencia */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}
</style>

<!-- INSTRUCCIONES DE IMPLEMENTACIÓN -->
<!--
1. PARA TU INDEX.HTML:
   - Copia la navegación y pégala después de <body>
   - Copia el CSS y agrégalo a tu <style> existente
   - En la navegación, el enlace "Inicio" debe tener la clase activa

2. ESTRUCTURA RECOMENDADA:
   index.html (página principal)
   ├── Navegación con "Inicio" activo
   ├── Tu contenido actual
   └── Footer con enlaces

   bot_commands.html (página de comandos)
   ├── Navegación con "Comandos" activo
   ├── Estadísticas en tiempo real
   ├── Lista de comandos
   └── Footer con enlaces

3. ENLACES IMPORTANTES:
   - index.html: Tu página principal actual
   - bot_commands.html: Nueva página de comandos
   - https://twitch.tv/flozwer: Tu canal de Twitch
   - https://github.com/Fl0zWer/Paimon: Tu repositorio

4. COLORES CONSISTENTES:
   - Fondo: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)
   - Primario: #4ecdc4 (turquesa)
   - Secundario: #ff6b6b (coral)
   - Acento: #45b7d1 (azul)
   - Texto: #b8c6db (gris azulado)

5. EFECTOS VISUALES:
   - backdrop-filter: blur(15px) para cristal
   - Gradientes animados en títulos
   - Hover effects con transform y box-shadow
   - Bordes con border-image gradients
-->

<!-- EJEMPLO DE CÓMO SE VERÁ LA NAVEGACIÓN -->
<!--
[🏠 Inicio]  [📋 Comandos]  [📺 Twitch]  [💻 GitHub]
     ↑              ↑
  (activo)    (página actual)
-->

<!-- CÓDIGO JAVASCRIPT OPCIONAL PARA EFECTOS ADICIONALES -->
<script>
// Agregar efectos de partículas de fondo (opcional)
document.addEventListener('DOMContentLoaded', function() {
    // Efecto de hover mejorado para la navegación
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
        });
        
        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Smooth scroll para enlaces internos
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

<!-- NOTAS FINALES -->
<!--
- Ambas páginas ahora tienen el mismo estilo visual
- La navegación permite moverse fácilmente entre páginas
- Los colores y efectos son consistentes
- El diseño es completamente responsivo
- Los efectos de hover y animaciones coinciden

Para implementar:
1. Sube bot_commands.html a tu repositorio
2. Agrega la navegación a tu index.html
3. Agrega el CSS adicional a tu index.html
4. ¡Disfruta de tu sitio web completo!
-->
