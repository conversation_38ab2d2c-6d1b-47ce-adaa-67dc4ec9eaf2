"""
Interfaz Web para el Bot de Twitch con estilo Geometry Dash
Se ejecuta independientemente del bot
"""

from flask import Flask, render_template, jsonify
import threading
import webbrowser
import time
import datetime
import os
import json

app = Flask(__name__)

# Archivos de datos del bot
DATA_FILES = {
    'user_activity': 'data/user_activity.json',
    'user_data': 'data/user_data.json',
    'lista_ids': 'data/lista_ids.json',
    'sorteos': 'data/sorteos.json',
    'channel_points': 'data/channel_points.json'
}

def load_bot_data():
    """Carga los datos del bot desde archivos JSON"""
    data = {}
    for key, filepath in DATA_FILES.items():
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    data[key] = json.load(f)
            else:
                data[key] = {}
        except Exception as e:
            print(f"Error cargando {filepath}: {e}")
            data[key] = {}
    return data

class BotDashboard:
    def __init__(self):
        self.app = app
        self.start_time = datetime.datetime.now()
        self.setup_routes()
    
    def setup_routes(self):
        """Configura las rutas de la interfaz web"""
        
        @app.route('/')
        def dashboard():
            """Página principal del dashboard"""
            return render_template('dashboard.html')
        
        @app.route('/api/stats')
        def get_stats():
            """API para obtener estadísticas en tiempo real"""
            try:
                # Cargar datos actuales del bot
                bot_data = load_bot_data()

                # Obtener canal principal (primer canal en la lista)
                main_channel = list(bot_data['user_activity'].keys())[0] if bot_data['user_activity'] else "unknown"

                # Calcular uptime
                uptime = datetime.datetime.now() - self.start_time
                uptime_str = f"{uptime.seconds // 3600}h {(uptime.seconds % 3600) // 60}m"

                # Obtener usuarios activos
                active_users = []
                if main_channel in bot_data['user_activity']:
                    now = datetime.datetime.now()
                    for username, data in bot_data['user_activity'][main_channel].items():
                        # Convertir string de fecha a datetime si es necesario
                        last_activity = data.get("last_activity", now.isoformat())
                        if isinstance(last_activity, str):
                            try:
                                last_activity = datetime.datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                            except:
                                last_activity = now

                        time_diff = now - last_activity
                        if time_diff.total_seconds() <= 1800:  # 30 minutos
                            active_users.append({
                                "username": username,
                                "messages": data.get("messages", 0),
                                "points": data.get("points", 0),
                                "levels_sent": data.get("levels_sent", 0),
                                "is_active": time_diff.total_seconds() <= 600  # 10 minutos
                            })

                # Ordenar por mensajes
                active_users.sort(key=lambda x: x["messages"], reverse=True)

                # Obtener top contribuidores por puntos
                contributors = []
                if main_channel in bot_data['user_data']:
                    for username, data in bot_data['user_data'][main_channel].items():
                        contributors.append({
                            "username": username,
                            "points": data.get("points", 0),
                            "levels_submitted": data.get("levels_submitted", 0)
                        })

                contributors.sort(key=lambda x: x["points"], reverse=True)

                # Obtener cola de niveles
                queue = []
                if main_channel in bot_data['lista_ids']:
                    level_list = bot_data['lista_ids'][main_channel]
                    for i, level_data in enumerate(level_list[:10]):
                        if isinstance(level_data, list) and len(level_data) >= 2:
                            queue.append({
                                "position": i + 1,
                                "level_id": level_data[0],
                                "username": level_data[1]
                            })

                # Obtener info de sorteo
                raffle_info = None
                if main_channel in bot_data['sorteos'] and bot_data['sorteos'][main_channel].get("activo", False):
                    raffle = bot_data['sorteos'][main_channel]
                    raffle_info = {
                        "active": True,
                        "prize": raffle.get("premio", "Premio desconocido"),
                        "participants": len(raffle.get("participantes", [])),
                        "participant_list": list(raffle.get("participantes", []))
                    }
                else:
                    raffle_info = {"active": False}
                
                return jsonify({
                    "success": True,
                    "channel": main_channel,
                    "uptime": uptime_str,
                    "total_active": len(active_users),
                    "top_chatters": active_users[:10],
                    "top_contributors": contributors[:8],
                    "level_queue": queue,
                    "raffle": raffle_info,
                    "timestamp": datetime.datetime.now().strftime("%H:%M:%S")
                })
                
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e)
                })
        
        @app.route('/api/channel_points')
        def get_channel_points():
            """API para obtener información de channel points"""
            try:
                bot_data = load_bot_data()
                main_channel = list(bot_data['channel_points'].keys())[0] if bot_data['channel_points'] else "unknown"

                rewards = []
                if main_channel in bot_data['channel_points']:
                    channel_rewards = bot_data['channel_points'][main_channel].get("rewards", {})
                    for reward_id, reward_data in channel_rewards.items():
                        rewards.append({
                            "name": reward_data.get("name", "Recompensa desconocida"),
                            "cost": reward_data.get("cost", 0),
                            "description": reward_data.get("description", ""),
                            "enabled": reward_data.get("enabled", True)
                        })

                return jsonify({
                    "success": True,
                    "channel": main_channel,
                    "rewards": rewards
                })

            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e)
                })
    
    def start_server(self):
        """Inicia el servidor web en un hilo separado"""
        def run_server():
            try:
                # Ejecutar en modo silencioso
                import logging
                log = logging.getLogger('werkzeug')
                log.setLevel(logging.ERROR)
                
                self.app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
            except Exception as e:
                print(f"Error iniciando servidor web: {e}")
        
        # Iniciar servidor en hilo separado
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar un momento y abrir navegador
        time.sleep(2)
        try:
            webbrowser.open('http://127.0.0.1:5000')
            print("🌐 Interfaz web abierta en: http://127.0.0.1:5000")
        except Exception as e:
            print(f"No se pudo abrir el navegador automáticamente: {e}")
            print("🌐 Abre manualmente: http://127.0.0.1:5000")

def create_dashboard():
    """Función para crear e iniciar la interfaz web"""
    # Crear directorio de datos si no existe
    os.makedirs('data', exist_ok=True)

    dashboard = BotDashboard()
    dashboard.start_server()
    return dashboard

if __name__ == "__main__":
    print("🌐 Iniciando Dashboard del Bot...")
    create_dashboard()
