"""
Interfaz Web para el Bot de Twitch con estilo Geometry Dash
Se ejecuta automáticamente cuando inicia el bot
"""

from flask import Flask, render_template, jsonify
import threading
import webbrowser
import time
import datetime
import os

app = Flask(__name__)

class BotDashboard:
    def __init__(self, bot_instance, user_activity, user_data, lista_ids_per_channel, sorteos_activos, channel_points_config):
        self.bot = bot_instance
        self.app = app
        self.user_activity = user_activity
        self.user_data = user_data
        self.lista_ids_per_channel = lista_ids_per_channel
        self.sorteos_activos = sorteos_activos
        self.channel_points_config = channel_points_config
        self.setup_routes()
    
    def setup_routes(self):
        """Configura las rutas de la interfaz web"""
        
        @app.route('/')
        def dashboard():
            """Página principal del dashboard"""
            return render_template('dashboard.html')
        
        @app.route('/api/stats')
        def get_stats():
            """API para obtener estadísticas en tiempo real"""
            try:
                # Obtener canal principal (primer canal en la lista)
                main_channel = list(self.user_activity.keys())[0] if self.user_activity else "unknown"
                
                # Calcular uptime
                uptime = datetime.datetime.now() - self.bot.start_time
                uptime_str = f"{uptime.seconds // 3600}h {(uptime.seconds % 3600) // 60}m"
                
                # Obtener usuarios activos
                active_users = []
                if main_channel in self.user_activity:
                    now = datetime.datetime.now()
                    for username, data in self.user_activity[main_channel].items():
                        time_diff = now - data["last_activity"]
                        if time_diff.total_seconds() <= 1800:  # 30 minutos
                            active_users.append({
                                "username": username,
                                "messages": data.get("messages", 0),
                                "points": data.get("points", 0),
                                "levels_sent": data.get("levels_sent", 0),
                                "is_active": time_diff.total_seconds() <= 600  # 10 minutos
                            })
                
                # Ordenar por mensajes
                active_users.sort(key=lambda x: x["messages"], reverse=True)
                
                # Obtener top contribuidores por puntos
                contributors = []
                if main_channel in self.user_data:
                    for username, data in self.user_data[main_channel].items():
                        contributors.append({
                            "username": username,
                            "points": data.get("points", 0),
                            "levels_submitted": data.get("levels_submitted", 0)
                        })
                
                contributors.sort(key=lambda x: x["points"], reverse=True)
                
                # Obtener cola de niveles
                queue = []
                if main_channel in self.lista_ids_per_channel:
                    for i, (level_id, username) in enumerate(self.lista_ids_per_channel[main_channel][:10]):
                        queue.append({
                            "position": i + 1,
                            "level_id": level_id,
                            "username": username
                        })
                
                # Obtener info de sorteo
                raffle_info = None
                if main_channel in self.sorteos_activos and self.sorteos_activos[main_channel]["activo"]:
                    raffle = self.sorteos_activos[main_channel]
                    raffle_info = {
                        "active": True,
                        "prize": raffle["premio"],
                        "participants": len(raffle["participantes"]),
                        "participant_list": list(raffle["participantes"])
                    }
                else:
                    raffle_info = {"active": False}
                
                return jsonify({
                    "success": True,
                    "channel": main_channel,
                    "uptime": uptime_str,
                    "total_active": len(active_users),
                    "top_chatters": active_users[:10],
                    "top_contributors": contributors[:8],
                    "level_queue": queue,
                    "raffle": raffle_info,
                    "timestamp": datetime.datetime.now().strftime("%H:%M:%S")
                })
                
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e)
                })
        
        @app.route('/api/channel_points')
        def get_channel_points():
            """API para obtener información de channel points"""
            try:
                main_channel = list(self.channel_points_config.keys())[0] if self.channel_points_config else "unknown"
                
                rewards = []
                if main_channel in self.channel_points_config:
                    for reward_id, reward_data in self.channel_points_config[main_channel]["rewards"].items():
                        rewards.append({
                            "name": reward_data["name"],
                            "cost": reward_data["cost"],
                            "description": reward_data.get("description", ""),
                            "enabled": reward_data.get("enabled", True)
                        })
                
                return jsonify({
                    "success": True,
                    "channel": main_channel,
                    "rewards": rewards
                })
                
            except Exception as e:
                return jsonify({
                    "success": False,
                    "error": str(e)
                })
    
    def start_server(self):
        """Inicia el servidor web en un hilo separado"""
        def run_server():
            try:
                # Ejecutar en modo silencioso
                import logging
                log = logging.getLogger('werkzeug')
                log.setLevel(logging.ERROR)
                
                self.app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
            except Exception as e:
                print(f"Error iniciando servidor web: {e}")
        
        # Iniciar servidor en hilo separado
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar un momento y abrir navegador
        time.sleep(2)
        try:
            webbrowser.open('http://127.0.0.1:5000')
            print("🌐 Interfaz web abierta en: http://127.0.0.1:5000")
        except Exception as e:
            print(f"No se pudo abrir el navegador automáticamente: {e}")
            print("🌐 Abre manualmente: http://127.0.0.1:5000")

def create_dashboard(bot_instance, user_activity, user_data, lista_ids_per_channel, sorteos_activos, channel_points_config):
    """Función para crear e iniciar la interfaz web"""
    dashboard = BotDashboard(bot_instance, user_activity, user_data, lista_ids_per_channel, sorteos_activos, channel_points_config)
    dashboard.start_server()
    return dashboard
