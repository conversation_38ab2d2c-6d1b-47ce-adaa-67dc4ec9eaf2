#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para obtener el Bot ID automáticamente
"""

import requests
import json

def get_bot_id_from_api():
    """Obtiene el Bot ID usando la API de Twitch"""
    
    # Configuración
    username = "guisodepaimon"
    client_id = "gp762nuuoqcoxypju8c569th9wz7q5"
    token = "fxyp5yny604oheoipw08d6bvnjw0tz"  # Sin el "oauth:" prefix
    
    print("🔍 Obteniendo Bot ID para:", username)
    print("=" * 50)
    
    try:
        # Headers para la API de Twitch
        headers = {
            'Authorization': f'Bearer {token}',
            'Client-Id': client_id
        }
        
        # Hacer petición a la API
        url = f'https://api.twitch.tv/helix/users?login={username}'
        response = requests.get(url, headers=headers)
        
        print(f"📡 Respuesta de la API: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('data') and len(data['data']) > 0:
                user_info = data['data'][0]
                bot_id = user_info['id']
                display_name = user_info['display_name']
                
                print("✅ ¡Bot ID obtenido exitosamente!")
                print(f"👤 Usuario: {display_name}")
                print(f"🆔 Bot ID: {bot_id}")
                print("")
                print("📋 COPIA ESTE VALOR:")
                print(f"BOT_ID = '{bot_id}'")
                print("")
                print("📝 Reemplaza en PBot.py línea 21:")
                print(f"BOT_ID = '{bot_id}'  # User ID del bot")
                
                return bot_id
            else:
                print("❌ Usuario no encontrado")
                return None
                
        elif response.status_code == 401:
            print("❌ Token inválido o expirado")
            print("💡 Necesitas renovar el token OAuth")
            return None
            
        else:
            print(f"❌ Error en la API: {response.status_code}")
            print(f"📄 Respuesta: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")
        return None
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return None

def get_bot_id_manual():
    """Instrucciones para obtener Bot ID manualmente"""
    print("🔧 MÉTODO MANUAL:")
    print("=" * 50)
    print("1. Ve a: https://www.streamweasels.com/tools/convert-twitch-username-to-user-id/")
    print("2. Ingresa: guisodepaimon")
    print("3. Copia el User ID que aparece")
    print("4. Reemplaza en PBot.py línea 21:")
    print("   BOT_ID = 'EL_ID_QUE_COPIASTE'")
    print("")

def update_pbot_file(bot_id):
    """Actualiza automáticamente el archivo PBot.py"""
    try:
        # Leer archivo actual
        with open('PBot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Reemplazar BOT_ID
        old_line = "BOT_ID = 'tu_bot_id_aqui'"
        new_line = f"BOT_ID = '{bot_id}'"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Guardar archivo
            with open('PBot.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Archivo PBot.py actualizado automáticamente!")
            print(f"📝 BOT_ID configurado como: {bot_id}")
            return True
        else:
            print("⚠️ No se pudo actualizar automáticamente")
            print("📝 Actualiza manualmente la línea:")
            print(f"BOT_ID = '{bot_id}'")
            return False
            
    except Exception as e:
        print(f"❌ Error actualizando archivo: {e}")
        return False

def main():
    """Función principal"""
    print("🤖 Obtener Bot ID para Paimon Bot")
    print("=" * 50)
    
    # Intentar obtener automáticamente
    bot_id = get_bot_id_from_api()
    
    if bot_id:
        print("")
        print("🔄 ¿Quieres que actualice automáticamente PBot.py? (s/n): ", end="")
        try:
            respuesta = input().lower().strip()
            if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
                if update_pbot_file(bot_id):
                    print("")
                    print("🎉 ¡Configuración completada!")
                    print("🚀 Ahora solo necesitas configurar CLIENT_SECRET")
                    print("📖 Ve a: configurar_bot_twitch.md para más detalles")
                else:
                    print("")
                    print("📝 Actualiza manualmente PBot.py con:")
                    print(f"BOT_ID = '{bot_id}'")
            else:
                print("")
                print("📝 Actualiza manualmente PBot.py con:")
                print(f"BOT_ID = '{bot_id}'")
        except KeyboardInterrupt:
            print("\n👋 Operación cancelada")
    else:
        print("")
        get_bot_id_manual()
    
    print("")
    print("💡 RECUERDA:")
    print("- También necesitas configurar CLIENT_SECRET")
    print("- O usar: python paimon_bot_integrated.py (más fácil)")

if __name__ == "__main__":
    main()
