# 🌐 Interfaz Web del Bot Paimon - Estilo Geometry Dash

## ✨ Características

La nueva interfaz web del bot Paimon tiene un diseño inspirado en **Geometry Dash** con:

- 🎨 **Colores vibrantes** y efectos visuales llamativos
- 🌈 **Bordes animados** con efectos de arcoíris
- 📊 **Estadísticas en tiempo real** del bot y usuarios
- 🎮 **Cola de niveles** con posiciones numeradas
- 🎁 **Estado de sorteos** activos
- ⭐ **Top contributors** y chatters más activos
- 🔄 **Actualización automática** cada 5 segundos

## 🚀 Cómo Ejecutar

### Opción 1: Solo Interfaz Web (Recomendado por ahora)
```bash
python bot_dashboard.py
```

### Opción 2: Bot + Interfaz Web (Cuando se solucione TwitchIO)
```bash
python run_bot_with_interface.py
```

## 📁 Estructura de Archivos

```
bot twitch/
├── bot_dashboard.py          # Interfaz web independiente
├── templates/
│   └── dashboard.html        # Template HTML con estilo GD
├── data/                     # Datos del bot (JSON)
│   ├── user_activity.json    # Actividad de usuarios
│   ├── user_data.json        # Datos de puntos y logros
│   ├── lista_ids.json        # Cola de niveles
│   ├── sorteos.json          # Sorteos activos
│   └── channel_points.json   # Configuración de recompensas
└── PBot.py                   # Bot principal (con guardado de datos)
```

## 🎯 Funcionalidades de la Interfaz

### 📊 Panel de Estadísticas
- **Canal activo**: Muestra el canal principal
- **Tiempo activo**: Uptime del bot
- **Usuarios activos**: Cantidad de usuarios en línea

### 💬 Top Chatters
- Lista de usuarios más activos por mensajes
- Muestra puntos y niveles enviados
- Actualización en tiempo real

### 🎮 Cola de Niveles
- Posición numerada de cada nivel
- ID del nivel y usuario que lo envió
- Máximo 10 niveles mostrados

### 🎁 Sistema de Sorteos
- Estado del sorteo (activo/inactivo)
- Premio del sorteo actual
- Número de participantes
- Animación especial cuando hay sorteo activo

### ⭐ Top Contributors
- Usuarios con más puntos
- Niveles enviados por cada usuario
- Ranking por contribución

## 🎨 Estilo Visual

### Colores Principales
- **Fondo**: Gradiente azul (#00d4ff → #0066ff)
- **Tarjetas**: Gradiente naranja (#ff6600 → #ff9900)
- **Bordes**: Amarillo (#ffff00) con efectos animados
- **Texto**: Blanco con sombras negras
- **Acentos**: Cian (#00ffff), Verde (#00ff00), Magenta (#ff00ff)

### Efectos Especiales
- **Bordes arcoíris**: Animación de colores rotativa
- **Sorteo activo**: Efecto de pulso y colores especiales
- **Fuente**: Fredoka One (estilo Geometry Dash)
- **Sombras**: Múltiples capas para efecto 3D

## 🔧 Configuración

### Datos de Ejemplo
La interfaz incluye datos de ejemplo para demostración:
- 5 usuarios activos con diferentes niveles de actividad
- 7 niveles en cola
- 1 sorteo activo con 7 participantes
- 5 recompensas de Channel Points configuradas

### Personalización
Puedes modificar:
- **Colores**: Editar el CSS en `templates/dashboard.html`
- **Datos**: Modificar archivos JSON en la carpeta `data/`
- **Frecuencia de actualización**: Cambiar el intervalo en JavaScript (línea 280)

## 🌐 Acceso

Una vez ejecutado, la interfaz estará disponible en:
**http://127.0.0.1:5000**

El navegador se abrirá automáticamente al iniciar la interfaz.

## 🔄 Actualización de Datos

La interfaz se actualiza automáticamente cada 5 segundos y muestra:
- ⏰ **Timestamp** de última actualización (esquina superior derecha)
- 🔄 **Botón de actualización manual** (esquina inferior derecha)

## 🎯 Próximas Mejoras

- 🔧 Solucionar compatibilidad con TwitchIO
- 📱 Diseño responsive para móviles
- 🎵 Efectos de sonido estilo Geometry Dash
- 📈 Gráficos de estadísticas históricas
- 🎨 Más temas visuales personalizables

## 🐛 Solución de Problemas

### La interfaz no carga
1. Verificar que Flask esté instalado: `pip install flask`
2. Verificar que el puerto 5000 esté libre
3. Revisar que exista la carpeta `data/` con archivos JSON

### Datos no se actualizan
1. Verificar que los archivos JSON en `data/` tengan el formato correcto
2. Revisar permisos de escritura en la carpeta `data/`
3. Comprobar la consola del navegador para errores JavaScript

---

¡Disfruta de tu nueva interfaz web con estilo Geometry Dash! 🎮✨
