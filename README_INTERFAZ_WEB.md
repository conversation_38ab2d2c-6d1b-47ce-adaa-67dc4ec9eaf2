# 🌟 Interfaz Web del Bot Paimon - Estilo Genshin Impact

## 🎨 Descripción

Esta es una interfaz web **minimalista y elegante** con estilo **Genshin Impact** para el bot de Twitch Paimon. Presenta ventanas flotantes completamente personalizables que puedes mover, redimensionar y organizar según tus necesidades para streaming.

## ✨ Características Principales

### 🪟 Ventanas Flotantes Personalizables
- **Completamente móviles** - Arrastra desde la barra superior
- **Redimensionables** - Ajusta el tamaño según necesites
- **Minimizables/Maximizables** - Controles de ventana estilo sistema
- **Ocultables** - Botón para mostrar/ocultar todas las ventanas
- **Posiciones guardadas** - Botón para resetear a posiciones por defecto

### 🎨 Diseño Genshin Impact
- **Colores elegantes** - Azul profundo, dorado y púrpura
- **Efectos de cristal** - Backdrop blur y transparencias
- **Animaciones suaves** - Transiciones fluidas y efectos hover
- **Tipografía moderna** - Fuente Inter limpia y legible
- **Efectos de brillo** - Ventanas con glow para elementos activos

### 📊 Ventanas Disponibles
1. **📊 Estadísticas del Canal** - Canal, uptime, usuarios activos
2. **💬 Top Chatters** - Usuarios más activos con mensajes y puntos
3. **🎮 Cola de Niveles** - Lista numerada con IDs y autores
4. **🎁 Sorteo Activo** - Estado, premio y participantes (con efecto glow)
5. **⭐ Top Contributors** - Ranking por puntos y contribuciones

## 🚀 Instalación y Uso

### Requisitos
```bash
pip install flask
```

### Ejecutar la Interfaz
```bash
python bot_dashboard.py
```

La interfaz se abrirá automáticamente en: **http://127.0.0.1:5000**

## 🎮 Controles de la Interfaz

### Panel de Control (Esquina Superior Derecha)
- **👁️ Mostrar/Ocultar** - Alterna visibilidad de todas las ventanas
- **🔄 Resetear** - Restaura posiciones y tamaños por defecto
- **📡 Actualizar** - Actualización manual de datos

### Controles de Ventana
Cada ventana tiene 3 botones en la esquina superior derecha:
- **🟡 Minimizar** - Colapsa la ventana a solo la barra superior
- **🟢 Maximizar** - Restaura el tamaño completo de la ventana
- **🔴 Cerrar** - Oculta completamente la ventana

### Interacción
- **Arrastrar** - Haz clic y arrastra desde la barra superior de cualquier ventana
- **Redimensionar** - Arrastra desde las esquinas/bordes de las ventanas
- **Scroll** - Las ventanas con mucho contenido tienen scroll personalizado

## 📁 Estructura de Archivos

```
├── bot_dashboard.py          # Servidor web Flask
├── templates/
│   └── dashboard.html        # Template HTML estilo Genshin
├── data/                     # Datos de ejemplo (JSON)
│   ├── stats.json
│   ├── chatters.json
│   ├── queue.json
│   ├── raffle.json
│   └── contributors.json
└── README_INTERFAZ_WEB.md    # Esta documentación
```

## 🎯 Perfecto para Streaming

### Ventajas para OBS/Streaming
- **Ventanas independientes** - Puedes capturar solo las que necesites
- **Tamaños personalizables** - Ajusta cada ventana al espacio disponible
- **Posicionamiento libre** - Coloca las ventanas donde mejor se vean
- **Fondo transparente** - Las ventanas se integran bien con overlays
- **Estilo minimalista** - No distrae del contenido principal

### Configuración Recomendada para Stream
1. **Posiciona las ventanas** donde no interfieran con el juego
2. **Ajusta los tamaños** según el espacio disponible en tu layout
3. **Oculta ventanas** que no necesites en ciertos momentos
4. **Usa el botón resetear** para volver a una configuración conocida

## 🎨 Personalización de Colores

### Paleta Genshin Impact
- **Fondo principal**: `#1a1a2e` (Azul oscuro)
- **Acentos dorados**: `#ffd700` (Oro Genshin)
- **Acentos púrpura**: `#8a2be2` (Púrpura místico)
- **Acentos azules**: `#1e90ff` (Azul brillante)

### Modificar Colores
Puedes cambiar los colores en `templates/dashboard.html` buscando estas variables CSS:
```css
/* Colores principales */
background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
border: 2px solid rgba(255, 215, 0, 0.3); /* Bordes dorados */
color: #ffd700; /* Texto dorado */
```

## 🔄 Actualización de Datos

- **Automática**: Cada 5 segundos
- **Manual**: Botón "📡 Actualizar" en el panel de control
- **Indicador**: Esquina inferior derecha muestra última actualización

## 🛠️ Solución de Problemas

### Las ventanas no se mueven
1. Asegúrate de arrastrar desde la **barra superior** (no el contenido)
2. Verifica que JavaScript esté habilitado en tu navegador

### Las ventanas se salen de la pantalla
1. Usa el botón **"🔄 Resetear"** para restaurar posiciones
2. O refresca la página (F5)

### Los datos no se actualizan
1. Verifica que los archivos JSON existan en `data/`
2. Revisa la consola del navegador (F12) para errores
3. Usa el botón de actualización manual

## 📱 Responsive Design

La interfaz se adapta a diferentes tamaños de pantalla:
- **Desktop**: Experiencia completa con todas las funciones
- **Tablet**: Ventanas más pequeñas, controles adaptados
- **Mobile**: Interfaz optimizada para pantallas pequeñas

## 🎯 Próximas Mejoras

- [ ] Guardar posiciones de ventanas en localStorage
- [ ] Temas de color personalizables
- [ ] Ventanas adicionales (comandos, moderación)
- [ ] Integración con alertas de stream
- [ ] Modo pantalla completa para ventanas individuales

---

**¡Disfruta de tu dashboard estilo Genshin Impact completamente personalizable!** 🌟✨

### 💡 Tip para Streamers
*Configura las ventanas una vez según tu layout de stream, y usa el botón "Resetear" para volver siempre a esa configuración perfecta.*
